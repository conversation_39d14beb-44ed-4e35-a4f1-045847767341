import requests
import json
import time  # ⬅ Added for timing

# BASE_URL = "http://localhost:8000"  # Change if running on a different server
BASE_URL = "https://chatbots.botswork.ai"

ENDPOINTS = {
    "1": "/email-chat",
    "2": "/crm-chat",
    "3": "/receptionist-chat",
    "4": "/research-chat",
    "5": "/social-media-chat",
    "6": "/phone-chat"
}

def chat_with_bot(endpoint, user_id):
    print(f"\n💬 Chatting with: {endpoint}")
    print("Type 'exit' to stop this chatbot.\n")

    while True:
        message = input("You: ").strip()
        if message.lower() in ["exit", "quit"]:
            break

        payload = {
            "user_id": user_id,
            "message": message
        }

        try:
            start_time = time.perf_counter()  # ⏱ Start timer
            response = requests.post(
                BASE_URL + endpoint,
                json=payload
            )
            end_time = time.perf_counter()  # ⏱ End timer

            elapsed_time = end_time - start_time

            if response.status_code == 200:
                data = response.json()
                print(f"🤖 Bot: {data.get('response')}")
                print(f"⏳ Response time: {elapsed_time:.2f} seconds ({elapsed_time*1000:.0f} ms)")
            else:
                print(f"❌ Error {response.status_code}: {response.text}")
                print(f"⏳ Response time: {elapsed_time:.2f} seconds")
        except Exception as e:
            print(f"⚠️ Request failed: {e}")

def main():
    print("=== Botswork V2 Chatbot Tester ===")
    print("use rohan's id: 67fa1bc157ad70418b0aa241\n")
    user_id = input("Enter user_id: ").strip()
    if not user_id:
        print("User ID cannot be empty!")
        return

    while True:
        print("\nSelect a chatbot to test:")
        print("1. Email Chatbot")
        print("2. CRM Chatbot")
        print("3. Receptionist Chatbot")
        print("4. Research Chatbot")
        print("5. Social Media Chatbot")
        print("6. Phone Chatbot")
        print("7. Exit")

        choice = input("Enter your choice: ").strip()

        if choice == "7":
            print("Goodbye!")
            break
        elif choice in ENDPOINTS:
            chat_with_bot(ENDPOINTS[choice], user_id)
        else:
            print("❌ Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
