from openai import OpenAI
import os
import json
from dotenv import load_dotenv
import logging
from typing import Optional
import re
import requests
import time

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Load environment variables from .env
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
X_API_KEY = os.getenv("X_API_KEY")
BASE_URL = os.getenv("BASE_URL")

# Validate environment variables
if not OPENAI_API_KEY:
    logger.error("OPENAI_API_KEY is not set in .env file")
    raise ValueError("OPENAI_API_KEY is not set in .env file")
if not X_API_KEY:
    logger.error("X_API_KEY is not set in .env file")
    raise ValueError("X_API_KEY is not set in .env file")
if not BASE_URL:
    logger.error("BASE_URL is not set in .env file")
    raise ValueError("BASE_URL is not set in .env file")

# Initialize OpenAI client
client = OpenAI(api_key=OPENAI_API_KEY)

# Load prompts
def load_prompts() -> dict:
    """Load prompts from prompts.json file."""
    try:
        with open("prompts.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error("prompts.json file not found")
        raise FileNotFoundError("prompts.json file not found")
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing prompts.json: {str(e)}")
        raise json.JSONDecodeError(f"Error parsing prompts.json: {str(e)}", e.doc, e.pos)
    except Exception as e:
        logger.error(f"Unexpected error loading prompts.json: {str(e)}")
        raise Exception(f"Unexpected error loading prompts.json: {str(e)}")

prompts = load_prompts()
ORIGINAL_SYSTEM_PROMPT = prompts.get("original_system_prompt", "")
CATEGORY_EXTRACTION_PROMPT = prompts.get("category_extraction_prompt", "")
INTENT_DETECTION_PROMPT = prompts.get("intent_detection_prompt", "")
EMAIL_EXTRACTION_PROMPT = prompts.get("email_extraction_prompt", "")

CONVERSATIONS_FILE = "email_conversations.json"

def fetch_bearer_token(user_id: str) -> Optional[str]:
    """Fetch bearer token from the auth/user/token API for the given user ID."""
    url = f"{BASE_URL}/auth/user/token?userId={user_id}"
    headers = {
        "accept": "*/*",
        "X-API-Key": X_API_KEY
    }
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        data = response.json()
        if data.get("status") == "SUCCESS" and "result" in data:
            token = data["result"]
            return token
        else:
            logger.error(f"Unexpected response format or status from token API for user_id {user_id}")
            return None
    except requests.RequestException as e:
        logger.error(f"Error fetching bearer token for user_id {user_id}: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response content: {e.response.text}")
        return None

def fetch_existing_categories(user_id: str) -> list:
    """Fetch existing categories from the email-bot-functions/list API."""
    token = fetch_bearer_token(user_id)
    if not token:
        logger.error(f"No valid bearer token for user_id {user_id}, returning empty list")
        return []

    url_get = f"{BASE_URL}/email-bot-functions/list"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "accept": "*/*"
    }
    try:
        response = requests.get(url_get, headers=headers)
        response.raise_for_status()
        data = response.json()
        if data.get("status") == "SUCCESS" and "result" in data:
            names = [item["name"] for item in data["result"]]
            return names
        else:
            logger.error(f"Unexpected response format or status from GET request for user_id {user_id}")
            return []
    except requests.RequestException as e:
        logger.error(f"Error fetching existing categories for user_id {user_id}: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response content: {e.response.text}")
        return []

def is_list_categories_request(message: str) -> bool:
    """Check if the user's message is a request to list categories."""
    list_phrases = [
        r"\b(list|show|display)\s+(out\s+)?categories\b",
        r"\bwhat\s+are\s+the\s+categories\b",
        r"\b(current|existing)\s+categories\b"
    ]
    message = message.lower().strip()
    return any(re.search(phrase, message, re.IGNORECASE) for phrase in list_phrases)

def is_email_send_confirmation(message: str) -> bool:
    """Check if the assistant's message confirms an email was sent."""
    return "✅ Great! Your email draft has been sent to" in message

def extract_email_details(draft_message: str, max_retries: int = 5, retry_delay: float = 1.0) -> Optional[dict]:
    """Extract to, subject, and content from the email draft using OpenAI API."""
    attempt = 1
    while attempt <= max_retries:
        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": EMAIL_EXTRACTION_PROMPT},
                    {"role": "user", "content": draft_message}
                ],
                max_tokens=500,
                temperature=0.5
            )
            result = response.choices[0].message.content.strip()
            
            parsed = json.loads(result)
            if all(key in parsed for key in ["to", "subject", "content"]):
                return parsed
            else:
                logger.error(f"Attempt {attempt}: Missing required fields in email extraction response: {result}")
        
        except json.JSONDecodeError as e:
            logger.error(f"Attempt {attempt}: Error parsing email extraction JSON: {str(e)} - Response: {result}")
        except Exception as e:
            logger.error(f"Attempt {attempt}: Unknown error in email extraction: {str(e)}")
        
        if attempt == max_retries:
            logger.error(f"Max retries ({max_retries}) reached for email extraction. Returning None.")
            return None
        attempt += 1
        time.sleep(retry_delay)
    
    return None

def send_email_to_api(to: str, subject: str, content: str, user_id: str):
    """Send email details to the mail API."""
    token = fetch_bearer_token(user_id)
    if not token:
        logger.error(f"No valid bearer token for user_id {user_id}, cannot send email")
        return

    url = f"{BASE_URL}/mail"
    headers = {
        "Authorization": f"Bearer {token}",
        "accept": "*/*"
    }
    data = {
        "toAddress": to,
        "subject": subject,
        "content": content,
        "mode": "",
        "replyTo": "",
        "attachments": "",
        "bccAddress": "",
        "ccAddress": "",
        "action": "",
        "askReceipt": ""
    }
    try:
        response = requests.post(url, headers=headers, files={k: (None, v) for k, v in data.items()})
        response.raise_for_status()
        response_data = response.json()
    except requests.RequestException as e:
        logger.error(f"Failed to send email to {to} for user_id {user_id}: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response content: {e.response.text}")

def send_email_to_api_task(draft_message: str, user_id: str):
    """Background task to extract email details and send to mail API."""
    email_details = extract_email_details(draft_message)
    if email_details:
        send_email_to_api(
            to=email_details["to"],
            subject=email_details["subject"],
            content=email_details["content"],
            user_id=user_id
        )
    else:
        logger.error(f"Failed to extract valid email details for user_id {user_id} from draft: {draft_message}")

def load_conversations() -> dict:
    """Load conversations from JSON file, or initialize an empty dict if file doesn't exist."""
    try:
        if os.path.exists(CONVERSATIONS_FILE):
            with open(CONVERSATIONS_FILE, "r") as f:
                return json.load(f)
        return {}
    except Exception as e:
        logger.error(f"Error loading conversations: {str(e)}")
        return {}

def save_conversations(conversations: dict):
    """Save conversations to JSON file."""
    try:
        with open(CONVERSATIONS_FILE, "w") as f:
            json.dump(conversations, f, indent=2)
    except Exception as e:
        logger.error(f"Error saving conversations: {str(e)}")

def is_confirmation_message(message: str) -> bool:
    """Check if the user's message is an affirmative confirmation."""
    confirmation_phrases = [
        r"\b(yes|correct|that's right|confirm|okay|ok|yeah|yep)\b",
        r"\b(looks good|fine|all good|good to go)\b"
    ]
    message = message.lower().strip()
    return any(re.search(phrase, message, re.IGNORECASE) for phrase in confirmation_phrases)

def is_category_confirmation_request(message: str) -> bool:
    """Check if the assistant's message is a category confirmation request (for filtering or deletion)."""
    return (
        "You want to filter your emails into these categories:" in message or
        "You want to delete these categories:" in message or
        "You want to delete this category" in message 
    )

def extract_categories(conversation: list, max_retries: int = 10, retry_delay: float = 1.0) -> Optional[str]:
    """Extract categories from conversation history using OpenAI API."""
    input_text = "Conversation History:\n"
    for message in conversation:
        role = message["role"]
        content = message["content"]
        input_text += f"{role.capitalize()}: {content}\n"

    attempt = 1
    while attempt <= max_retries:
        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": CATEGORY_EXTRACTION_PROMPT},
                    {"role": "user", "content": input_text}
                ],
                max_tokens=500,
                temperature=0.5
            )
            categories = response.choices[0].message.content.strip()
            
            if categories == "None":
                return None
            else:
                parsed = json.loads(categories)
                result = json.dumps(parsed)
                return result
        
        except json.JSONDecodeError as e:
            logger.error(f"Attempt {attempt}: Error parsing categories JSON: {str(e)} - Response: {categories}")
            if attempt == max_retries:
                logger.error(f"Max retries ({max_retries}) reached. Returning None.")
                return None
            attempt += 1
            time.sleep(retry_delay)
        except Exception as e:
            logger.error(f"Attempt {attempt}: Unknown error: {str(e)}")
            return None
    
    logger.error(f"Max retries ({max_retries}) reached. Returning None.")
    return None

def getting_bot_function_and_comparing(categories: Optional[str], user_id: str) -> dict:
    """Fetch bot functions, compare with categories, post missing categories to APIs, and return them."""
    token = fetch_bearer_token(user_id)
    if not token:
        logger.error(f"No valid bearer token for user_id {user_id}, returning empty dict")
        return {}

    url_get = f"{BASE_URL}/email-bot-functions/list"
    url_post_email = f"{BASE_URL}/email-bot-functions"
    url_post_interpretation = f"{BASE_URL}/email-bot-functions/interpretation"
    url_detailed = f"{BASE_URL}/email-bot-function-detailed"
    url_situation = f"{BASE_URL}/email-bot-function-detailed/situation"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "accept": "*/*"
    }
    try:
        response = requests.get(url_get, headers=headers)
        response.raise_for_status()
        data = response.json()
        
        if data.get("status") == "SUCCESS" and "result" in data:
            names = [item["name"] for item in data["result"]]
            
            if categories:
                categories_dict = json.loads(categories)
                missing_categories = {
                    category: situation_block 
                    for category, situation_block in categories_dict.items()
                    if category not in names
                }
                
                for category, situation_block in missing_categories.items():
                    # Name: Convert dashes to spaces, then title-case it
                    name = category.replace("-", " ").title()
                    
                    # Value: Convert to lowercase, replace dashes/spaces with underscores
                    value = category.lower().replace("-", "_").replace(" ", "_")

                    payload_email = {
                        "value": value,
                        "name": name
                    }
                    try:
                        post_response_email = requests.post(url_post_email, headers=headers, json=payload_email)
                        post_response_email.raise_for_status()
                        response_data_email = post_response_email.json()
                        
                        if response_data_email.get("status") == "SUCCESS" and "result" in response_data_email:
                            result = response_data_email["result"]
                            response_value = result.get("value")
                            response_name = result.get("name")
                            if response_value and response_name:
                                payload_interpretation = {
                                    "botFunctionsValue": response_value,
                                    "value": response_value,
                                    "name": response_name
                                }
                                try:
                                    post_response_interpretation = requests.post(url_post_interpretation, headers=headers, json=payload_interpretation)
                                    post_response_interpretation.raise_for_status()
                                    response_data_interpretation = post_response_interpretation.json()
                                    
                                    if response_data_interpretation.get("status") == "SUCCESS" and "result" in response_data_interpretation:
                                        interpretation_id = response_data_interpretation["result"].get("_id")
                                        if interpretation_id:
                                            detailed_url = f"{url_detailed}?interpretationId={interpretation_id}"
                                            try:
                                                detailed_response = requests.get(detailed_url, headers=headers)
                                                detailed_response.raise_for_status()
                                                response_data_detailed = detailed_response.json()
                                                
                                                if response_data_detailed.get("status") == "SUCCESS" and "result" in response_data_detailed:
                                                    detailed_id = response_data_detailed["result"].get("_id")
                                                    if detailed_id:
                                                        payload_situation = {
                                                            "botFunctionDetailedId": detailed_id,
                                                            "name": situation_block
                                                        }
                                                        try:
                                                            situation_response = requests.post(url_situation, headers=headers, json=payload_situation)
                                                            situation_response.raise_for_status()
                                                            response_data_situation = situation_response.json()
                                                        except requests.RequestException as e:
                                                            logger.error(f"Failed to post to situation API for category '{category}' with botFunctionDetailedId '{detailed_id}' for user_id {user_id}: {str(e)}")
                                                            if hasattr(e, 'response') and e.response is not None:
                                                                logger.error(f"Response content: {e.response.text}")
                                                            else:
                                                                print(f"POST error for category '{category}' to email-bot-function-detailed/situation with botFunctionDetailedId '{detailed_id}': {str(e)}")
                                                    else:
                                                        logger.error(f"Missing '_id' in email-bot-function-detailed response for category '{category}' for user_id {user_id}")
                                                else:
                                                    logger.error(f"Unexpected response format from email-bot-function-detailed for category '{category}' for user_id {user_id}")
                                            except requests.RequestException as e:
                                                logger.error(f"Failed to fetch detailed bot function for category '{category}' with interpretationId '{interpretation_id}' for user_id {user_id}: {str(e)}")
                                                if hasattr(e, 'response') and e.response is not None:
                                                    logger.error(f"Response content: {e.response.text}")
                                                else:
                                                    print(f"GET error for category '{category}' from email-bot-function-detailed with interpretationId '{interpretation_id}': {str(e)}")
                                        else:
                                            logger.error(f"Missing '_id' in interpretation API response for category '{category}' for user_id {user_id}")
                                    else:
                                        logger.error(f"Unexpected response format from interpretation API for category '{category}' for user_id {user_id}")
                                except requests.RequestException as e:
                                    logger.error(f"Failed to post to interpretation API for category '{category}' for user_id {user_id}: {str(e)}")
                                    if hasattr(e, 'response') and e.response is not None:
                                        logger.error(f"Response content: {e.response.text}")
                                    else:
                                        print(f"POST error for category '{category}' to interpretation API: {str(e)}")
                            else:
                                logger.error(f"Missing 'value' or 'name' in email-bot-functions response for category '{category}' for user_id {user_id}")
                        else:
                            logger.error(f"Unexpected response format from email-bot-functions for category '{category}' for user_id {user_id}")
                    except requests.RequestException as e:
                        logger.error(f"Failed to post category '{category}' to email-bot-functions for user_id {user_id}: {str(e)}")
                        if hasattr(e, 'response') and e.response is not None:
                            logger.error(f"Response content: {e.response.text}")
                        else:
                            print(f"POST error for category '{category}' to email-bot-functions: {str(e)}")
                
                return missing_categories
            else:
                return {}
        else:
            logger.error(f"Unexpected response format or status from GET request for user_id {user_id}")
            return {}
    except requests.RequestException as e:
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response content: {e.response.text}")
        return {}
    except json.JSONDecodeError as e:
        return {}

def sending_bot_functions_and_situation_block(last_assistant_message: str, user_message: str, categories: Optional[str], user_id: str):
    try:
        if categories:
            categories_dict = json.loads(categories)
            for category, situation_block in categories_dict.items():
                print(f"Category: {category}, Situation Block: {situation_block}")
        else:
            print(f"Category: None, Situation Block: None")
        
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": INTENT_DETECTION_PROMPT},
                {
                    "role": "user",
                    "content": f"Assistant's last message: {last_assistant_message}\nUser's current message: {user_message}"
                }
            ],
            max_tokens=10,
            temperature=0.5
        )
        intent = response.choices[0].message.content.strip()
        
        if intent == "update":
            missing_categories = getting_bot_function_and_comparing(categories, user_id)
        
        return intent
    except Exception as e:
        logger.error(f"Error detecting intent for user_id {user_id}: {str(e)}")
        return None