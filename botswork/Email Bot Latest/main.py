from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from models import ChatMessage, ChatResponse
from email_bot import (
    load_conversations, save_conversations, is_list_categories_request,
    is_category_confirmation_request, is_confirmation_message, extract_categories,
    sending_bot_functions_and_situation_block, is_email_send_confirmation,
    send_email_to_api_task, client,fetch_existing_categories,ORIGINAL_SYSTEM_PROMPT
)
import logging
import json

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/chat", response_model=ChatResponse)
async def chat(message: ChatMessage, background_tasks: BackgroundTasks):
    try:
        conversations = load_conversations()
        
        if message.user_id not in conversations:
            existing_categories = fetch_existing_categories(message.user_id)
            categories_str = ", ".join(existing_categories) if existing_categories else "None"
            no_categories_message = " There are no categories added. You can add categories through this chatbot." if not existing_categories else ""
            welcome_message = (
                f"Hi! This is your Email Bot. I can help you organize your emails by applying custom filters. "
                f"Current categories: {categories_str}.{no_categories_message} You can also delete existing categories "
                f"or create email drafts. Please provide the filters or categories you'd like to use, request to delete "
                f"categories, or describe an email draft you need."
            )
            conversations[message.user_id] = [
                {"role": "system", "content": ORIGINAL_SYSTEM_PROMPT},
                {
                    "role": "assistant",
                    "content": welcome_message,
                    "categories": None
                }
            ]
            save_conversations(conversations)
            return ChatResponse(
                user_id=message.user_id,
                response=conversations[message.user_id][-1]["content"]
            )
        
        conversations[message.user_id].append({"role": "user", "content": message.message})
        
        if is_list_categories_request(message.message):
            existing_categories = fetch_existing_categories(message.user_id)
            categories_str = ", ".join(existing_categories) if existing_categories else "None"
            assistant_message = f"Current categories: {categories_str}."
            categories_json = json.dumps(existing_categories) if existing_categories else None
            conversations[message.user_id].append({
                "role": "assistant",
                "content": assistant_message,
                "categories": categories_json
            })
            save_conversations(conversations)
            return ChatResponse(
                user_id=message.user_id,
                response=assistant_message
            )
        
        categories = conversations[message.user_id][-2]["categories"] if len(conversations[message.user_id]) >= 2 else None
        run_category_extraction = False
        if len(conversations[message.user_id]) >= 2:
            last_assistant_message = conversations[message.user_id][-2]["content"]
            if is_category_confirmation_request(last_assistant_message) and is_confirmation_message(message.message):
                run_category_extraction = True
        
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=conversations[message.user_id],
            max_tokens=500,
            temperature=0.7
        )
        assistant_message = response.choices[0].message.content
        
        if run_category_extraction:
            categories = extract_categories(conversation=conversations[message.user_id])
            background_tasks.add_task(sending_bot_functions_and_situation_block, last_assistant_message, message.message, categories, message.user_id)
        
        conversations[message.user_id].append({
            "role": "assistant",
            "content": assistant_message,
            "categories": categories
        })
        
        # Check if the response confirms an email was sent
        if is_email_send_confirmation(assistant_message) and len(conversations[message.user_id]) >= 4:
            # Get the previous assistant response (the draft)
            draft_message = conversations[message.user_id][-3]["content"]
            background_tasks.add_task(
                send_email_to_api_task,
                draft_message,
                message.user_id
            )
        
        save_conversations(conversations)
        
        return ChatResponse(
            user_id=message.user_id,
            response=assistant_message
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)