# Operating System files
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# IDE and Editor files
.idea/             # IntelliJ IDEA files
.vscode/           # VS Code files
*.sublime-project
*.sublime-workspace
.project           # Eclipse files
.classpath
.settings/
Chatbots/.env

# Build artifacts
bin/
obj/
*.lo
*.la
*.so
*.a
*.dll
*.o
*.mod
*.exe
*.out
*.app
*.apk
*.ipa
*.dmg

# Dependencies
node_modules/      # Node.js
vendor/            # PHP Composer, Ruby Bundler (often ignored)
env/               # Generic virtual environment
venv/              # Python virtual environment
.venv/             # Python virtual environment (common alternative)
test/              # Python virtual environment

# Compiled Python files
*.pyc
__pycache__/
.mypy_cache/
.pytest_cache/
.coverage

# Python specific files
pip-log.txt
pip-delete-this-directory.txt
.Python
build/
dist/
*.egg-info/
.tox/
.env             
.flaskenv
instance/          

# Node.js specific files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pids/              # pid files
logs/              # log files
*.log
*.seed
*.pid
*.tgz
.npm/

# Visual Studio / .NET
[Bb]in/
[Oo]bj/
*.user
*.suo
*.sln.docstates
*.builds
*.opendata
*.vs/
_ReSharper.*/
*.DotSettings
*.csproj.user
*.resharper
*.vssscc
*.vspscc
*.vshost

# macOS specific
.Spotlight-V100
.Trashes
Icon?

# Windows specific
$Recycle.Bin/
System Volume Information/

# Other common ignored files
*.swp                # Vim swap files
*.bak                # Backup files
*.tmp                # Temporary files
*.swo
