from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from pydantic import BaseModel
from openai import OpenAI
import os
import json
from dotenv import load_dotenv
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional
import logging
import re
import requests
import time
from models_receptionist import ChatMessage, ChatResponse  # Import from your models.py

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
X_API_KEY = os.getenv("X_API_KEY")
BASE_URL = os.getenv("BASE_URL")

if not OPENAI_API_KEY:
    logger.error("OPENAI_API_KEY is not set in .env file")
    raise ValueError("OPENAI_API_KEY is not set in .env file")
if not X_API_KEY:
    logger.error("X_API_KEY is not set in .env file")
    raise ValueError("X_API_KEY is not set in .env file")
if not BASE_URL:
    logger.error("BASE_URL is not set in .env file")
    raise ValueError("BASE_URL is not set in .env file")

def fetch_bearer_token(user_id: str, x_api_key: str, base_url: str) -> Optional[str]:
    url = f"{base_url}/auth/user/token?userId={user_id}"
    headers = {"accept": "*/*", "X-API-Key": x_api_key}
    logger.debug(f"Fetching bearer token: {url}, headers={headers}")
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        data = response.json()
        logger.debug(f"Token response: {data}")
        if data.get("status") == "SUCCESS" and "result" in data:
            return data["result"]
        logger.warning(f"Token fetch failed: status={data.get('status')}, result={data.get('result')}")
        return None
    except requests.RequestException as e:
        logger.error(f"Error fetching token: {e}")
        return None

def load_prompts() -> dict:
    """Load prompts from prompts_receptionist.json file."""
    try:
        with open("prompts_receptionist.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error("prompts_receptionist.json file not found")
        raise FileNotFoundError("prompts_receptionist.json file not found")
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing prompts_receptionist.json: {str(e)}")
        raise json.JSONDecodeError(f"Error parsing prompts_receptionist.json: {str(e)}", e.doc, e.pos)
    except Exception as e:
        logger.error(f"Unexpected error loading prompts_receptionist.json: {str(e)}")
        raise Exception(f"Unexpected error loading prompts_receptionist.json: {str(e)}")

prompts = load_prompts()
BASE_SYSTEM_PROMPT = prompts.get("base_system_prompt", "")
DATA_EXTRACTION_PROMPT = prompts.get("data_extraction_prompt", "")
INTENT_DETECTION_PROMPT = prompts.get("intent_detection_prompt", "")
MESSAGE_BLOCK_PROMPT = prompts.get("message_block_prompt", "")
BOT_FUNCTION_PROMPT = prompts.get("bot_function_prompt", "")
WELCOME_GENERATION_PROMPT = prompts.get("welcome_generation_prompt", "")
COMPLETION_PROMPT = prompts.get("completion_prompt", "")
ERROR_HANDLING_PROMPT = prompts.get("error_handling_prompt", "")

client = OpenAI(api_key=OPENAI_API_KEY)

CONVERSATIONS_FILE = "conversations.json"

class ConversationState:
    """Track the state of conversation for each user"""
    INITIAL = "initial"
    COLLECTING_MESSAGE_BLOCK = "collecting_message_block"
    COLLECTING_BOT_FUNCTIONS = "collecting_bot_functions"
    WAITING_FUNCTION_CONFIRMATION = "waiting_function_confirmation"
    MESSAGE_BLOCK_POSTED = "message_block_posted"
    BOT_FUNCTION_POSTED = "bot_function_posted"
    COMPLETED = "completed"

def get_conversation_state(conversations: dict, user_id: str) -> str:
    """Get the current state of conversation for a user"""
    if user_id not in conversations:
        return ConversationState.INITIAL
    
    user_conv = conversations[user_id]
    # Get the last message's state if it exists
    if isinstance(user_conv, list) and len(user_conv) > 1:
        last_message = user_conv[-1]
        if isinstance(last_message, dict) and "state" in last_message:
            return last_message["state"]
    
    return ConversationState.INITIAL

def get_latest_extracted_data(conversation: list) -> Optional[dict]:
    """Get the latest extracted data from conversation history"""
    for message in reversed(conversation):
        if isinstance(message, dict) and message.get("role") == "assistant":
            extracted_data = message.get("extracted_data")
            if extracted_data:
                try:
                    return json.loads(extracted_data)
                except:
                    pass
    return None

def create_dynamic_system_prompt(state: str, extracted_data: dict = None) -> str:
    """Create dynamic system prompt based on conversation state"""
    
    if state == ConversationState.INITIAL:
        return BASE_SYSTEM_PROMPT + "\n\n" + MESSAGE_BLOCK_PROMPT

    elif state == ConversationState.COLLECTING_MESSAGE_BLOCK:
        return BASE_SYSTEM_PROMPT + "\n\n" + MESSAGE_BLOCK_PROMPT

    elif state == ConversationState.COLLECTING_BOT_FUNCTIONS or state == ConversationState.WAITING_FUNCTION_CONFIRMATION:
        current_functions = []
        if extracted_data and "bot_functions" in extracted_data:
            current_functions = extracted_data["bot_functions"]
        
        function_count = len(current_functions)
        return BASE_SYSTEM_PROMPT + "\n\n" + BOT_FUNCTION_PROMPT.format(function_count=function_count)

    elif state in [ConversationState.MESSAGE_BLOCK_POSTED, ConversationState.BOT_FUNCTION_POSTED]:
        return f"""{BASE_SYSTEM_PROMPT}

Continue collecting bot functions. Ask about the next function or if they're done adding functions."""

    elif state == ConversationState.COMPLETED:
        return f"""{BASE_SYSTEM_PROMPT}

{COMPLETION_PROMPT}"""

    return BASE_SYSTEM_PROMPT

def generate_welcome_message(user_id: str, conversations: dict, client: OpenAI) -> str:
    """Generate dynamic welcome message using OpenAI"""
    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": WELCOME_GENERATION_PROMPT},
                {"role": "user", "content": f"Generate welcome message for user starting receptionist bot creation."}
            ],
            max_tokens=200,
            temperature=0.7
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        logger.error(f"Error generating welcome message: {str(e)}")
        return "Hello! I'll help you create a custom Receptionist bot. We'll start by defining your welcome message and then add specific functions. What kind of receptionist are you looking to create?"

def detect_intent(conversation: list, client: OpenAI) -> str:
    """Detect user intent using OpenAI"""
    try:
        # Get last few messages for context
        recent_messages = conversation[-4:] if len(conversation) >= 4 else conversation
        context = "\n".join([f"{msg.get('role', '')}: {msg.get('content', '')}" for msg in recent_messages])
        
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": INTENT_DETECTION_PROMPT},
                {"role": "user", "content": context}
            ],
            max_tokens=50,
            temperature=0.1
        )
        
        intent = response.choices[0].message.content.strip().lower()
        logger.info(f"Detected intent: {intent}")
        return intent
    except Exception as e:
        logger.error(f"Error detecting intent: {str(e)}")
        return "continue"

def load_conversations() -> dict:
    """Load conversations from JSON file"""
    try:
        if os.path.exists(CONVERSATIONS_FILE):
            with open(CONVERSATIONS_FILE, "r") as f:
                return json.load(f)
        return {}
    except Exception as e:
        logger.error(f"Error loading conversations: {str(e)}")
        return {}

def save_conversations(conversations: dict):
    """Save conversations to JSON file"""
    try:
        with open(CONVERSATIONS_FILE, "w") as f:
            json.dump(conversations, f, indent=2)
    except Exception as e:
        logger.error(f"Error saving conversations: {str(e)}")

def extract_message_block_data(conversation: list, client: OpenAI) -> Optional[dict]:
    """Extract message block data from conversation"""
    try:
        input_text = "Conversation History:\n"
        for message in conversation:
            role = message.get("role", "")
            content = message.get("content", "")
            input_text += f"{role.capitalize()}: {content}\n"

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": DATA_EXTRACTION_PROMPT},
                {"role": "user", "content": input_text + "\n\nExtract only MESSAGE_BLOCK data (message and logic). Return JSON or 'None'."}
            ],
            max_tokens=800,
            temperature=0.3
        )
        
        result = response.choices[0].message.content.strip()
        if result == "None":
            return None
        
        parsed = json.loads(result)
        if "message_block" in parsed and parsed["message_block"].get("message") and parsed["message_block"].get("logic"):
            logger.info(f"✅ Message block extracted successfully")
            return parsed["message_block"]
        return None
        
    except Exception as e:
        logger.error(f"❌ Error extracting message block: {str(e)}")
        return None

def extract_single_bot_function_data(conversation: list, client: OpenAI) -> Optional[dict]:
    """Extract single bot function data from conversation"""
    try:
        input_text = "Conversation History:\n"
        for message in conversation[-10:]:  # Only look at recent messages
            role = message.get("role", "")
            content = message.get("content", "")
            input_text += f"{role.capitalize()}: {content}\n"

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": DATA_EXTRACTION_PROMPT},
                {"role": "user", "content": input_text + "\n\nExtract the LATEST SINGLE bot function mentioned. Return JSON with one function or 'None'."}
            ],
            max_tokens=1000,
            temperature=0.3
        )
        
        result = response.choices[0].message.content.strip()
        if result == "None":
            return None
        
        parsed = json.loads(result)
        if "bot_functions" in parsed and len(parsed["bot_functions"]) > 0:
            logger.info(f"✅ Single bot function extracted successfully")
            return parsed["bot_functions"][0]  # Return just the first function
        return None
        
    except Exception as e:
        logger.error(f"❌ Error extracting single bot function: {str(e)}")
        return None

def check_message_block_complete(conversation: list, client: OpenAI) -> bool:
    """Check if message block is complete using OpenAI"""
    try:
        recent_messages = conversation[-6:] if len(conversation) >= 6 else conversation
        context = "\n".join([f"{msg.get('role', '')}: {msg.get('content', '')}" for msg in recent_messages])
        
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Analyze if user has provided both a welcome message and behavioral logic for a receptionist bot. Return only 'complete' or 'incomplete'."},
                {"role": "user", "content": context}
            ],
            max_tokens=20,
            temperature=0.1
        )
        
        result = response.choices[0].message.content.strip().lower()
        return result == "complete"
    except Exception as e:
        logger.error(f"Error checking message block completion: {str(e)}")
        return False

def check_single_bot_function_complete(conversation: list, client: OpenAI) -> bool:
    """Check if a single bot function is complete using OpenAI"""
    try:
        recent_messages = conversation[-8:] if len(conversation) >= 8 else conversation
        context = "\n".join([f"{msg.get('role', '')}: {msg.get('content', '')}" for msg in recent_messages])
        
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Analyze if user has provided complete information for ONE bot function including: function name, situation, prompt/understanding, and action. Return only 'complete' or 'incomplete'."},
                {"role": "user", "content": context}
            ],
            max_tokens=20,
            temperature=0.1
        )
        
        result = response.choices[0].message.content.strip().lower()
        return result == "complete"
    except Exception as e:
        logger.error(f"Error checking bot function completion: {str(e)}")
        return False

def post_conversation_block(message_block: dict, bearer_token: str) -> bool:
    """Post conversation block to BotsWork API"""
    url = f"{BASE_URL}/knowledge-base/conversation-block"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
        "accept": "*/*"
    }
    
    payload = {
        "message": message_block.get("message", ""),
        "logic": message_block.get("logic", ""),
        "agentType": "Receptionist"
    }
    
    try:
        logger.info(f"🚀 POSTING conversation block to: {url}")
        logger.info(f"📋 Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        response_data = response.json()
        
        logger.info(f"✅ CONVERSATION BLOCK CREATED SUCCESSFULLY!")
        logger.info(f"📊 Response: {json.dumps(response_data, indent=2)}")
        return True
        
    except requests.RequestException as e:
        logger.error(f"❌ FAILED to create conversation block: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response content: {e.response.text}")
        return False

def post_single_bot_function(bot_function: dict, bearer_token: str) -> bool:
    """Post single bot function to BotsWork API using the 6-step process"""
    base_url = f"{BASE_URL}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
        "accept": "*/*"
    }
    
    try:
        function_name = bot_function.get("bot_function", "")
        logger.info(f"🔧 Processing single function: {function_name}")
        
        function_value = function_name.replace(" ", "_").lower()
        
        # STEP 1: Create bot function
        function_payload = {"name": function_name, "value": function_value}
        logger.info(f"STEP 1 - Creating bot function: {function_payload}")
        
        function_response = requests.post(f"{base_url}/bot-functions", headers=headers, json=function_payload)
        function_response.raise_for_status()
        function_data = function_response.json()
        logger.info(f"✅ Bot function created: {function_data.get('result', {}).get('_id')}")
        
        # STEP 2: Create interpretation (sub-function)
        sub_function_name = bot_function.get("sub_bot_function", f"General {function_name}")
        sub_function_value = sub_function_name.replace(" ", "_").lower()
        
        interpretation_payload = {
            "botFunctionsValue": function_value,
            "name": sub_function_name,
            "value": sub_function_value
        }
        logger.info(f"STEP 2 - Creating interpretation: {interpretation_payload}")
        
        interpretation_response = requests.post(f"{base_url}/bot-functions/interpretation", headers=headers, json=interpretation_payload)
        interpretation_response.raise_for_status()
        interpretation_data = interpretation_response.json()
        interpretation_id = interpretation_data.get("result", {}).get("_id")
        logger.info(f"✅ Interpretation created with ID: {interpretation_id}")
        
        if not interpretation_id:
            logger.error(f"❌ No interpretation ID for {function_name}")
            return False
        
        # STEP 3: Get bot function detailed
        logger.info(f"STEP 3 - Getting detailed function for ID: {interpretation_id}")
        detailed_response = requests.get(f"{base_url}/bot-function-detailed?interpretationId={interpretation_id}", headers=headers)
        detailed_response.raise_for_status()
        detailed_data = detailed_response.json()
        bot_function_detailed_id = detailed_data.get("result", {}).get("_id")
        logger.info(f"✅ Detailed function ID: {bot_function_detailed_id}")
        
        if not bot_function_detailed_id:
            logger.error(f"❌ No detailed function ID for {function_name}")
            return False
        
        # STEP 4: Create situation
        cognitive_model = bot_function.get("cognitive_model", {})
        situation_name = cognitive_model.get("situation_block", "")
        
        if situation_name:
            situation_payload = {
                "botFunctionDetailedId": bot_function_detailed_id,
                "name": situation_name
            }
            logger.info(f"STEP 4 - Creating situation: {situation_payload}")
            
            situation_response = requests.post(f"{base_url}/bot-function-detailed/situation", headers=headers, json=situation_payload)
            situation_response.raise_for_status()
            logger.info(f"✅ Situation created")
        
        # STEP 5: Create prompt block
        prompt_message = cognitive_model.get("prompt_block", "")
        prompt_logic = cognitive_model.get("action_block", "")
        
        if prompt_message or prompt_logic:
            prompt_payload = {
                "botFunctionDetailedId": bot_function_detailed_id,
                "message": prompt_message,
                "logic": prompt_logic
            }
            logger.info(f"STEP 5 - Creating prompt block: {prompt_payload}")
            
            prompt_response = requests.put(f"{base_url}/bot-function-detailed/prompt-block", headers=headers, json=prompt_payload)
            prompt_response.raise_for_status()
            logger.info(f"✅ Prompt block created")
        
        logger.info(f"🎉 COMPLETED function: {function_name}")
        return True
        
    except requests.RequestException as e:
        logger.error(f"❌ API request failed: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response: {e.response.text}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        return False

def generate_completion_message(client: OpenAI) -> str:
    """Generate completion message using OpenAI"""
    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": COMPLETION_PROMPT},
                {"role": "user", "content": "Generate completion message for successful receptionist bot creation."}
            ],
            max_tokens=200,
            temperature=0.7
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        logger.error(f"Error generating completion message: {str(e)}")
        return "🎉 Your receptionist bot has been successfully created! All functions have been configured and are ready to use. Is there anything else you'd like to add or modify?"

def generate_error_message(client: OpenAI, error_context: str = "") -> str:
    """Generate error message using OpenAI"""
    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": ERROR_HANDLING_PROMPT},
                {"role": "user", "content": f"Generate error message for: {error_context}"}
            ],
            max_tokens=200,
            temperature=0.7
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        logger.error(f"Error generating error message: {str(e)}")
        return "I encountered an issue while processing your request. Let's try again or check the configuration. How can I help you proceed?"