from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks
from pydantic import BaseModel
from openai import OpenAI
import os
import json
from dotenv import load_dotenv
from fastapi.middleware.cors import CORSMiddleware
import logging
from chatbot_receptionist import (
    ConversationState, 
    check_single_bot_function_complete, 
    check_message_block_complete, 
    create_dynamic_system_prompt, 
    extract_single_bot_function_data, 
    extract_message_block_data, 
    generate_welcome_message, 
    generate_completion_message,
    generate_error_message,
    get_conversation_state, 
    get_latest_extracted_data, 
    load_conversations, 
    post_single_bot_function, 
    post_conversation_block, 
    save_conversations,
    fetch_bearer_token,
    detect_intent,
    X_API_KEY,
    BASE_URL
)
from models_receptionist import ChatMessage, ChatResponse

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
load_dotenv()

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

client = OpenAI(api_key=OPENAI_API_KEY)

@app.post("/chat_receptionist", response_model=ChatResponse)
async def chat_receptionist(message: ChatMessage, background_tasks: BackgroundTasks):
    try:
        bearer_token = fetch_bearer_token(message.user_id, X_API_KEY, BASE_URL)
        if not bearer_token:
            logger.error(f"❌ Failed to fetch bearer token for user: {message.user_id}")
            raise HTTPException(status_code=401, detail="Failed to authenticate user")
        
        logger.info(f"✅ Bearer token fetched successfully for user: {message.user_id}")
        
        conversations = load_conversations()
        
        current_state = get_conversation_state(conversations, message.user_id)
        
        if message.user_id not in conversations:
            welcome_message = generate_welcome_message(message.user_id, conversations, client)
            system_prompt = create_dynamic_system_prompt(ConversationState.INITIAL)
            
            conversations[message.user_id] = [
                {"role": "system", "content": system_prompt},
                {
                    "role": "assistant",
                    "content": welcome_message,
                    "extracted_data": None,
                    "state": ConversationState.COLLECTING_MESSAGE_BLOCK
                }
            ]
            save_conversations(conversations)
            return ChatResponse(
                user_id=message.user_id,
                response=welcome_message
            )
        
        conversations[message.user_id].append({"role": "user", "content": message.message})
        
        user_intent = detect_intent(conversations[message.user_id], client)
        logger.info(f"User intent detected: {user_intent}")
        
        if current_state == ConversationState.COLLECTING_MESSAGE_BLOCK:
            if check_message_block_complete(conversations[message.user_id], client):
                message_block = extract_message_block_data(conversations[message.user_id], client)
                
                if message_block:
                    if user_intent == "create":
                        success = post_conversation_block(message_block, bearer_token)
                        if success:
                            current_state = ConversationState.COLLECTING_BOT_FUNCTIONS
                            logger.info(f"✅ Message block posted successfully, transitioning to bot functions")
                        else:
                            error_message = generate_error_message(client, "posting message block")
                            conversations[message.user_id].append({
                                "role": "assistant",
                                "content": error_message,
                                "extracted_data": None,
                                "state": current_state
                            })
                            save_conversations(conversations)
                            return ChatResponse(user_id=message.user_id, response=error_message)
                    else:
                        current_state = ConversationState.COLLECTING_MESSAGE_BLOCK

        elif current_state == ConversationState.COLLECTING_BOT_FUNCTIONS:
            if check_single_bot_function_complete(conversations[message.user_id], client):
                bot_function = extract_single_bot_function_data(conversations[message.user_id], client)
                
                if bot_function:
                    if user_intent == "create":
                        success = post_single_bot_function(bot_function, bearer_token)
                        if success:
                            current_state = ConversationState.BOT_FUNCTION_POSTED
                            logger.info(f"✅ Bot function posted successfully")
                            
                            extracted_data = get_latest_extracted_data(conversations[message.user_id]) or {}
                            if "bot_functions" not in extracted_data:
                                extracted_data["bot_functions"] = []
                            extracted_data["bot_functions"].append(bot_function)
                            
                        else:
                            error_message = generate_error_message(client, "posting bot function")
                            conversations[message.user_id].append({
                                "role": "assistant",
                                "content": error_message,
                                "extracted_data": None,
                                "state": current_state
                            })
                            save_conversations(conversations)
                            return ChatResponse(user_id=message.user_id, response=error_message)
                    else:
                        current_state = ConversationState.WAITING_FUNCTION_CONFIRMATION

        elif current_state == ConversationState.BOT_FUNCTION_POSTED:
            if user_intent == "create" and message.message.lower() in ["done", "finished", "complete", "no more", "that's all"]:
                current_state = ConversationState.COMPLETED
            else:
                current_state = ConversationState.COLLECTING_BOT_FUNCTIONS

        elif current_state == ConversationState.WAITING_FUNCTION_CONFIRMATION:
            if user_intent == "create":
                bot_function = extract_single_bot_function_data(conversations[message.user_id], client)
                if bot_function:
                    success = post_single_bot_function(bot_function, bearer_token)
                    if success:
                        current_state = ConversationState.BOT_FUNCTION_POSTED
                        logger.info(f"✅ Bot function posted successfully after confirmation")
                    else:
                        error_message = generate_error_message(client, "posting bot function")
                        conversations[message.user_id].append({
                            "role": "assistant",
                            "content": error_message,
                            "extracted_data": None,
                            "state": current_state
                        })
                        save_conversations(conversations)
                        return ChatResponse(user_id=message.user_id, response=error_message)
            else:
                current_state = ConversationState.COLLECTING_BOT_FUNCTIONS

        extracted_data = get_latest_extracted_data(conversations[message.user_id])
        
        new_system_prompt = create_dynamic_system_prompt(current_state, extracted_data)
        conversations[message.user_id][0]["content"] = new_system_prompt
        
        if current_state == ConversationState.COMPLETED:
            assistant_message = generate_completion_message(client)
        else:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=conversations[message.user_id],
                max_tokens=400,
                temperature=0.7
            )
            assistant_message = response.choices[0].message.content
        
        conversations[message.user_id].append({
            "role": "assistant",
            "content": assistant_message,
            "extracted_data": json.dumps(extracted_data) if extracted_data else None,
            "state": current_state
        })
        
        save_conversations(conversations)
        
        return ChatResponse(
            user_id=message.user_id,
            response=assistant_message
        )
    
    except Exception as e:
        logger.error(f"❌ Error processing chat request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)