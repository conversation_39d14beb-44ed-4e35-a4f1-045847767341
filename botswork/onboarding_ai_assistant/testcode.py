import pytest
import httpx

BASE_URL = "http://localhost:8000"

@pytest.mark.asyncio
async def test_chat_endpoint():
    # Prepare a sample request payload
    payload = {
        "user_id": "test_user_123",
        "message": "What is the next step for my business plan?",
        "session_id": None,
        "metadata": {
            "industry": "technology",
            "stage": "idea"
        }
    }

    # Send the POST request
    async with httpx.AsyncClient() as client:
        response = await client.post(f"{BASE_URL}/chat", json=payload)

    # Check the response
    assert response.status_code == 200

    data = response.json()
    print("Response Data:", data)

    # Basic structure checks
    assert "message" in data
    assert "message_id" in data
    assert "session_id" in data
    assert "timestamp" in data

    # Optional fields check (if included)
    if data.get("next_questions"):
        assert isinstance(data["next_questions"], list)
    if data.get("suggested_options"):
        assert isinstance(data["suggested_options"], list)
    if data.get("business_plan_update"):
        assert isinstance(data["business_plan_update"], dict)
    if data.get("visuals"):
        assert isinstance(data["visuals"], dict)
