

from __future__ import annotations
import os
import json
import time
import traceback
from typing import Any, Dict, List, Optional
from research_fn import to_underscore, fetch_bearer_token
import requests
from openai import OpenAI
from bs4 import BeautifulSoup


# =========================
# HTTP utility with retries
# =========================

with open("prompt_research.json", "r", encoding="utf-8") as f:
    CONFIG = json.load(f)

SYSTEM_PROMPT = CONFIG["system_prompt"]
TOOLS = CONFIG["tools"]


def http_request(method: str, url: str, *, params=None, json_body=None, headers=None, timeout=20, retries=2) -> Optional[requests.Response]:
    for attempt in range(retries + 1):
        try:
            resp = requests.request(method=method.upper(), url=url, params=params, json=json_body, headers=headers, timeout=timeout)
            if 200 <= resp.status_code < 300:
                return resp
        except requests.RequestException:
            pass
        time.sleep(0.6 * (attempt + 1))
    return None


# =========================
# Platform API client
# =========================

class BotsWorkClient:
    """
    Minimal client that calls your own platform endpoints.
    Configure BW_API_BASE and BW_API_KEY. Override paths as needed.
    """

    def __init__(self):
        self.base = os.getenv("BW_API_BASE", "").rstrip("/")
        self.api_key = os.getenv("BW_API_KEY", "")
        if not self.base:
            raise RuntimeError("BW_API_BASE is not set.")
        if not self.api_key:
            raise RuntimeError("BW_API_KEY is not set.")

        # Endpoints (append to base). Override via env if your routes differ.
        self.comp_path = os.getenv("BW_COMP_ANALYSIS_PATH", "/competitive-analysis")
        self.trend_path = os.getenv("BW_TREND_PATH", "/trend-analysis")
        self.topic_path = os.getenv("BW_TOPIC_PATH", "/topic-research")

    @property
    def _headers(self) -> Dict[str, str]:
        # Adjust auth header shape to your platform convention if different
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

    def competitor_benchmarking(self, payload: Dict[str, Any]) -> Dict[str, Any]:

        resp = http_request("POST", "https://research-bot.botswork.ai/competitive-analysis/", json_body=payload)
        return self._json_or_error(resp)

    def trend_analysis_by_title(self, payload: Dict[str, Any], token) -> Dict[str, Any]:
        """
        Expected payload suggestion:
        { "title": "Agentic AI in customer support" }
        """
        header={"Authorization": f"Bearer {token}"}
        resp = http_request("POST", "https://api.botswork.ai/api/botsworkai/researchbot-functions/interpretation", json_body=payload, headers=header)
        return self._json_or_error(resp)

    def topic_research(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Expected payload suggestion:
        { "topic": "Agentic AI in customer support" }
        """
        header={"Authorization": f"Bearer {token}"}
        resp = http_request("POST", f"https://api.botswork.ai/api/botsworkai/researchbot-functions/interpretation", json_body=payload, headers=header)
        return self._json_or_error(resp)

    @staticmethod
    def _json_or_error(resp: Optional[requests.Response]) -> Dict[str, Any]:
        if resp is None:
            return {"ok": False, "error": "network_error", "message": "No response from platform API"}
        try:
            data = resp.json()
        except Exception:
            return {"ok": False, "status": resp.status_code, "text": resp.text}
        # Normalize
        return {"ok": (200 <= resp.status_code < 300), "status": resp.status_code, "data": data}


# =========================
# Optional extras (public)
# =========================

def wikipedia_summary(topic: str) -> Optional[Dict[str, Any]]:
    api = f"https://en.wikipedia.org/api/rest_v1/page/summary/{topic.strip().replace(' ', '%20')}"
    resp = http_request("GET", api, timeout=12)
    if not resp:
        return None
    try:
        data = resp.json()
    except Exception:
        return None
    if "extract" in data:
        return {
            "title": data.get("title"),
            "description": data.get("description"),
            "extract": data.get("extract"),
            "url": data.get("content_urls", {}).get("desktop", {}).get("page"),
        }
    return None


def crossref_search(query: str, rows: int = 6) -> List[Dict[str, Any]]:
    resp = http_request("GET", "https://api.crossref.org/works", params={"query": query, "rows": rows}, timeout=15)
    if not resp:
        return []
    try:
        items = resp.json().get("message", {}).get("items", [])
    except Exception:
        return []
    out = []
    for it in items:
        title = (it.get("title") or [""])[0]
        authors = [f"{a.get('given','')} {a.get('family','')}".strip() for a in it.get("author", [])] if it.get("author") else []
        doi = it.get("DOI")
        year = it.get("issued", {}).get("date-parts", [[None]])[0][0]
        url = it.get("URL") or (f"https://doi.org/{doi}" if doi else None)
        out.append({"title": title, "authors": authors, "year": year, "doi": doi, "url": url})
    return out


def hn_trend_samples(query: str, pages: int = 1) -> Dict[str, Any]:
    base = "https://hn.algolia.com/api/v1/search"
    counts: Dict[str, int] = {}
    examples: List[Dict[str, Any]] = []
    for page in range(pages):
        resp = http_request("GET", base, params={"query": query, "page": page, "tags": "story"}, timeout=12)
        if not resp:
            continue
        try:
            data = resp.json()
        except Exception:
            continue
        for hit in data.get("hits", []):
            year = (hit.get("created_at") or "")[:4]
            counts[year] = counts.get(year, 0) + 1
            if len(examples) < 6:
                examples.append({
                    "title": hit.get("title"),
                    "url": hit.get("url") or f"https://news.ycombinator.com/item?id={hit.get('objectID')}",
                    "points": hit.get("points"),
                    "year": year,
                })
    trend = [{"year": y, "count": counts[y]} for y in sorted(counts)]
    return {"query": query, "trend": trend, "examples": examples}


# =========================
# Tool implementations
# =========================

BW = None  # will be initialized lazily so the file can import without envs during tests

def tool_competitor_benchmarking(args: Dict[str, Any]) -> Dict[str, Any]:
    """
    Required: name, url
    Optional: description
    base_url is fixed (set once, not from args)
    """
    global BW
    if BW is None:
        BW = BotsWorkClient()
    
    # Inject fixed base_url if not provided
    fixed_base_url = "https://api.botswork.ai/api/botsworkai/file/upload/PDF"
    payload = {
        "name": args.get("name", "Competitor Benchmarking"),
        "url": args.get("url"),
        "description": args.get("description", ""),
        "base_url": fixed_base_url
    }
    return BW.competitor_benchmarking(payload)


def tool_trend_analysis_by_title(args: Dict[str, Any], token) -> Dict[str, Any]:
    """
    Required: title
    """
    global BW
    if BW is None:
        BW = BotsWorkClient()

    title= args.get("title", "")
    payload = {
        "botFunctionsValue": "trend_business_analysis",
        "name":title,
        "value": to_underscore(title),
    }
    return BW.trend_analysis_by_title(payload, token)

def tool_topic_research_and_articles(args: Dict[str, Any], token) -> Dict[str, Any]:
    """
    Required: topic
    
    """
    global BW
    if BW is None:
        BW = BotsWorkClient()
    title= args.get("topic", "")
    payload = {
        "botFunctionsValue": "topic_research",
        "name":title,
        "value": to_underscore(title),
    }
    return BW.topic_research(payload, token)

def tool_research_articles_by_titles(args: Dict[str, Any]) -> Dict[str, Any]:
    """
    args: {"titles": ["paper 1", "paper 2", ...]}
    """
    titles: List[str] = args.get("titles") or []
    results = {}
    for t in titles:
        results[t] = crossref_search(t, rows=3)
    return {"results": results}

def tool_web_research(args: Dict[str, Any]) -> Dict[str, Any]:
    """
    General aggregator if the model wants broader context beyond your 3 platform calls.
    args: {"query": "..."}
    """
    q = args.get("query") or ""
    wiki = wikipedia_summary(q)
    cr = crossref_search(q, rows=5)
    hn = hn_trend_samples(q, pages=1)
    return {"query": q, "wikipedia": wiki, "crossref": cr, "hn": hn}


# =========================
# OpenAI tool schemas
# =========================




# =========================
# OpenAI orchestration
# =========================

def call_model_with_tools(user_query: str,token: Optional[str] = None, seed_context: Optional[List[Dict[str, Any]]] = None) -> str:

    client = OpenAI()

    system_msg = {
        "role": "system",
        "content":SYSTEM_PROMPT,
    }

    messages = [system_msg]
    if seed_context:
        messages.extend(seed_context)
    messages.append({"role": "user", "content": user_query})

    first = client.chat.completions.create(
        model="gpt-4o",
        messages=messages,
        tools=TOOLS,
        tool_choice="auto",
        temperature=0.2,
    )

    msg = first.choices[0].message
    tool_calls = getattr(msg, "tool_calls", None)
    # Run any requested tools
    tool_results_msgs: List[Dict[str, Any]] = []

    def run_tool(name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        try:
            if name == "competitor_benchmarking":
                return tool_competitor_benchmarking(arguments)
            if name == "trend_analysis_by_title":
                return tool_trend_analysis_by_title(arguments, token)
            if name == "topic_research_and_articles":
                return tool_topic_research_and_articles(arguments, token)
            if name == "research_articles_by_titles":
                return tool_research_articles_by_titles(arguments)
            if name == "web_research":
                return tool_web_research(arguments)
            return {"ok": False, "error": f"Unknown tool: {name}"}
        except Exception as e:
            return {"ok": False, "error": str(e), "traceback": traceback.format_exc()}

    if tool_calls:
        for tc in tool_calls:
            fn_name = tc.function.name
            try:
                args = json.loads(tc.function.arguments or "{}")
            except json.JSONDecodeError:
                args = {}
            out = run_tool(fn_name, args)
            tool_results_msgs.append({
                "role": "tool",
                "tool_call_id": tc.id,
                "name": fn_name,
                "content": json.dumps(out)
            })

        # Give results back to the model for synthesis
        final = OpenAI().chat.completions.create(
            model="gpt-4o",
            messages=messages + [
                {
                    "role": "assistant",
                    "content": msg.content or "",
                    "tool_calls": [
                        {"id": tc.id, "type": "function",
                         "function": {"name": tc.function.name, "arguments": tc.function.arguments}}
                        for tc in tool_calls
                    ],
                },
                *tool_results_msgs,
            ],
            temperature=0.2,
        )
        return final.choices[0].message.content or ""

    # No tools requested
    return msg.content or ""


# =========================
# Public convenience API
# =========================

def research_assistant(query: str, token: str) -> str:
    return call_model_with_tools(query, token)


# =========================
# CLI
# =========================

if __name__ == "__main__":
    import sys
    from dotenv import load_dotenv
    load_dotenv()
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

    if "OPENAI_API_KEY" not in os.environ:
        print("ERROR: Please set OPENAI_API_KEY.")
        sys.exit(1)

    # BW envs are loaded lazily on first tool call. That means you can run queries
    # that don’t require platform tools without setting BW_* envs. For platform calls,
    # BW_API_BASE and BW_API_KEY must be present.
    while True:
        user_query = input(">>> ")
        if user_query == "exit":
            break
        userid= "67fa1bc157ad70418b0aa241"
        token= fetch_bearer_token(userid)
        print(token)
        print("\n>>> USER:", user_query)
        print("\n--- Research Assistant ---\n")
        print(research_assistant(user_query, token))
        print("\n")
