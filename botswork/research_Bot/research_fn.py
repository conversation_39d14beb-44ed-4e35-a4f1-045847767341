import requests
from dotenv import load_dotenv
import os
from typing import Optional
import logging
import re
# Set up logging
logging.basicConfig(level=logging.ERROR)
logger = logging.getLogger(__name__)
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
X_API_KEY = os.getenv("X_API_KEY")
BASE_URL = os.getenv("BASE_URL")


def fetch_bearer_token(user_id: str) -> Optional[str]:
    """Fetch bearer token from the auth/user/token API for the given user ID."""
    url = f"https://api.botswork.ai/api/botsworkai/auth/user/token?userId={user_id}"
    headers = {
        "accept": "*/*",
        "X-API-Key": X_API_KEY
    }
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        data = response.json()
        if data.get("status") == "SUCCESS" and "result" in data:
            token = data["result"]
            return token
        else:
            logger.error(f"Unexpected response format or status from token API for user_id {user_id}")
            return None
    except requests.RequestException as e:
        logger.error(f"Error fetching bearer token for user_id {user_id}: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response content: {e.response.text}")
        return None
    


def to_underscore(s: str) -> str:
    return re.sub(r"\s+", "_", s.strip())




def strip_markdown(text: str) -> str:
    """
    Remove Markdown bold (**), italics (*), and heading (#) markers.
    Keeps plain text content only.
    """
    # Remove bold/italics markers
    text = re.sub(r"\*{1,2}([^*]+)\*{1,2}", r"\1", text)
    # Remove headings #
    text = re.sub(r"^#+\s*", "", text, flags=re.MULTILINE)
    return text
