import json
import os
import re
import requests
import time
from openai import OpenAI
from typing import Optional, Dict, List, Union
from fastapi import HTTPException
import logging

logging.basicConfig(level=logging.ERROR)
logger = logging.getLogger(__name__)

CONVERSATIONS_FILE = "conversations_crm.json"

def load_prompts() -> dict:
    try:
        with open("prompts_crm.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error("prompts_crm.json file not found")
        raise FileNotFoundError("prompts_crm.json file not found")
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in prompts_crm.json: {e}")
        raise ValueError(f"Invalid JSON in prompts_crm.json: {e}")

def fetch_bearer_token(user_id: str, x_api_key: str, base_url: str) -> Optional[str]:
    url = f"{base_url}/auth/user/token?userId={user_id}"
    headers = {"accept": "*/*", "X-API-Key": x_api_key}
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        data = response.json()
        if data.get("status") == "SUCCESS" and "result" in data:
            return data["result"]
        return None
    except requests.RequestException as e:
        logger.error(f"Error fetching token: {e}")
        return None

def fetch_existing_categories(user_id: str, x_api_key: str, base_url: str, max_retries: int = 3, retry_delay: float = 1.0) -> List[Dict[str, str]]:
    token = fetch_bearer_token(user_id, x_api_key, base_url)
    if not token:
        logger.error("No valid token retrieved")
        return []

    url = f"{base_url}/crm-bot-functions/list"
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json", "accept": "*/*"}
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            data = response.json()
            if data.get("status") == "SUCCESS" and "result" in data:
                categories = [{"name": item["name"], "id": item["_id"]} for item in data["result"]]
                return categories
            if attempt == max_retries - 1:
                logger.error("Max retries reached for fetching categories")
                return []
            time.sleep(retry_delay)
        except requests.RequestException as e:
            logger.error(f"Attempt {attempt + 1}: Error fetching categories: {e}")
            if attempt == max_retries - 1:
                logger.error("Max retries reached for fetching categories")
                return []
            time.sleep(retry_delay)
    return []

def is_list_categories_request(message: str) -> bool:
    list_phrases = [
        r"\b(list|show|display)(\s+me)?(\s+the)?(\s+list\s+of)?\s+(out\s+)?(\w+\s+)?functions\b",
        r"\bwhat\s+are\s+the\s+(functions|categories|bot\s+functions)\b",
        r"\b(current|existing)\s+(functions|categories|bot\s+functions)\b",
        r"\b(show|list)\s+me\s+the\s+(list\s+of\s+)?categories\b"
    ]
    return any(re.search(phrase, message.lower().strip(), re.IGNORECASE) for phrase in list_phrases)

def is_adding_function_request(message: str) -> bool:
    add_phrases = [
        r"\b(add|create|set\s+up)\s+.*\s*(function|functions|bot\s+function|bot\s+functions)\b",
        r"\bnew\s+(function|functions|bot\s+function|bot\s+functions)\b"
    ]
    return any(re.search(phrase, message.lower().strip(), re.IGNORECASE) for phrase in add_phrases)

def is_deleting_function_request(message: str) -> bool:
    delete_phrases = [
        r"\b(delete|remove)\s+.*\s*(function|functions|bot\s+function|bot\s+functions)\b",
        r"\bdelete\s+all\s+(functions|bot\s+functions)\b"
    ]
    return any(re.search(phrase, message.lower().strip(), re.IGNORECASE) for phrase in delete_phrases)

def is_confirmation_message(message: str) -> bool:
    confirmation_phrases = [
        r"\b(yes|correct|that's right|confirm|okay|ok|yeah|yep)\b",
        r"\b(looks good|fine|all good|good to go)\b"
    ]
    return any(re.search(phrase, message.lower().strip(), re.IGNORECASE) for phrase in confirmation_phrases)

def clean_function_name(name: str) -> str:
    if not name:
        return ""
    cleaned = name.strip().strip("'").strip('"')
    cleaned = re.sub(r'^[\'"]|[\'"]$', '', cleaned)
    return cleaned

def load_conversations() -> dict:
    try:
        if os.path.exists(CONVERSATIONS_FILE):
            with open(CONVERSATIONS_FILE, "r", encoding="utf-8") as f:
                return json.load(f)
        return {}
    except Exception as e:
        logger.error(f"Error loading conversations: {e}")
        return {}

def save_conversations(conversations: dict):
    max_retries = 3
    retry_delay = 1.0
    for attempt in range(max_retries):
        try:
            with open(CONVERSATIONS_FILE, "w", encoding="utf-8") as f:
                json.dump(conversations, f, indent=2)
                return
        except Exception as e:
            logger.error(f"Attempt {attempt + 1}: Error saving conversations: {e}")
            if attempt == max_retries - 1:
                logger.error("Max retries reached for saving conversations")
                raise
            time.sleep(retry_delay)

def extract_categories(
    client: OpenAI,
    conversation: list,
    user_situation_block: Optional[str] = None,
    current_function: Optional[str] = None,
    category_extraction_prompt: str = ""
) -> Optional[str]:
    if user_situation_block and current_function:
        try:
            for msg in reversed(conversation):
                if "describe when this function should trigger" in msg["content"].lower() and msg["role"] == "assistant":
                    patterns = [
                        r"I’d like to set up\s*(?:\"|\')?(.*?)(?:\"|\')?\s*for you\.",
                        r"for the CRM function:\s*(.*?)\.",
                        r"\bset\s+up\s*(?:\"|\')?(.*?)(?:\"|\')?\b"
                    ]
                    for pattern in patterns:
                        match = re.search(pattern, msg["content"], re.IGNORECASE)
                        if match and clean_function_name(match.group(1).strip()).lower() == current_function.lower():
                            return json.dumps({clean_function_name(current_function): user_situation_block})
            return None
        except Exception as e:
            logger.error(f"Error extracting categories with situation: {e}")
            return None
    else:
        input_text = "\n".join(f"{msg['role'].capitalize()}: {msg['content']}" for msg in conversation)
        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": category_extraction_prompt},
                    {"role": "user", "content": input_text}
                ],
                max_tokens=500,
                temperature=0.5
            )
            categories = response.choices[0].message.content.strip()
            if categories == "None":
                return None
            parsed = json.loads(categories)
            if isinstance(parsed, list):
                cleaned = [clean_function_name(name) for name in parsed if name and name.lower() not in ["none", "\"none\""]]
                return json.dumps(cleaned) if cleaned else None
            elif isinstance(parsed, dict):
                cleaned = {clean_function_name(k): v for k, v in parsed.items() if k and k.lower() not in ["none", "\"none\""]}
                return json.dumps(cleaned) if cleaned else None
            else:
                cleaned = clean_function_name(parsed)
                return json.dumps([cleaned]) if cleaned and cleaned.lower() not in ["none", "\"none\""] else None
        except Exception as e:
            logger.error(f"Error extracting categories: {e}")
            return None

def getting_bot_function_and_comparing(categories: Optional[str], user_id: str, x_api_key: str, base_url: str) -> Dict[str, List[str]]:
    token = fetch_bearer_token(user_id, x_api_key, base_url)
    if not token:
        logger.error("No valid token, cannot add functions")
        return {"existing": [], "added": [], "failed": []}

    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json", "accept": "*/*"}
    existing_functions = fetch_existing_categories(user_id, x_api_key, base_url)
    existing_names = [func["name"].lower() for func in existing_functions]

    result = {"existing": [], "added": [], "failed": []}
    if not categories:
        return result

    try:
        categories_dict = json.loads(categories)
        for category, situation_block in categories_dict.items():
            cleaned_category = clean_function_name(category)
            if not cleaned_category:
                result["failed"].append(category)
                continue
            if cleaned_category.lower() in existing_names:
                result["existing"].append(cleaned_category)
                continue
            name = cleaned_category.replace("-", " ").title()
            value = cleaned_category.lower().replace("-", "_").replace(" ", "_")
            payload_crm = {"value": value, "name": name}
            try:
                response = requests.post(f"{base_url}/crm-bot-functions", headers=headers, json=payload_crm)
                response.raise_for_status()
                data = response.json()
                if data.get("status") == "SUCCESS" and "result" in data:
                    payload_interpretation = {"botFunctionsValue": value, "value": value, "name": name}
                    interpretation_response = requests.post(
                        f"{base_url}/crm-bot-functions/interpretation", headers=headers, json=payload_interpretation
                    )
                    interpretation_response.raise_for_status()
                    interpretation_data = interpretation_response.json()
                    if interpretation_data.get("status") == "SUCCESS" and "result" in interpretation_data:
                        interpretation_id = interpretation_data["result"]["_id"]
                        detailed_response = requests.get(
                            f"{base_url}/crm-bot-function-detailed?interpretationId={interpretation_id}", headers=headers
                        )
                        detailed_response.raise_for_status()
                        detailed_data = detailed_response.json()
                        if detailed_data.get("status") == "SUCCESS" and "result" in detailed_data:
                            detailed_id = detailed_data["result"]["_id"]
                            payload_situation = {"botFunctionDetailedId": detailed_id, "name": situation_block}
                            situation_response = requests.post(
                                f"{base_url}/crm-bot-function-detailed/situation", headers=headers, json=payload_situation
                            )
                            situation_response.raise_for_status()
                            situation_data = situation_response.json()
                            for attempt in range(3):
                                updated_functions = fetch_existing_categories(user_id, x_api_key, base_url)
                                if any(func["name"].lower() == cleaned_category.lower() for func in updated_functions):
                                    result["added"].append(cleaned_category)
                                    break
                                time.sleep(1)
                            else:
                                result["failed"].append(cleaned_category)
                                logger.error(f"Failed to confirm addition of function: {cleaned_category}")
                        else:
                            result["failed"].append(cleaned_category)
                            logger.error(f"Failed to get detailed data for {cleaned_category}: {detailed_data}")
                    else:
                        result["failed"].append(cleaned_category)
                        logger.error(f"Failed to interpret function: {cleaned_category}: {interpretation_data}")
                else:
                    result["failed"].append(cleaned_category)
                    logger.error(f"Failed to add function: {cleaned_category}, response={data}")
            except requests.RequestException as e:
                result["failed"].append(cleaned_category)
                logger.error(f"Error adding function {cleaned_category}: {e}")
        return result
    except Exception as e:
        logger.error(f"Error processing categories: {e}")
        return result

def delete_bot_functions(functions: List[str], user_id: str, x_api_key: str, base_url: str) -> Dict[str, List[str]]:
    token = fetch_bearer_token(user_id, x_api_key, base_url)
    if not token:
        logger.error("No valid token, cannot delete functions")
        return {"deleted": [], "not_found": []}

    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json", "accept": "*/*"}
    existing_functions = fetch_existing_categories(user_id, x_api_key, base_url)
    existing_names = {func["name"].lower(): func["id"] for func in existing_functions}

    result = {"deleted": [], "not_found": []}
    for func in functions:
        cleaned_func = clean_function_name(func)
        if not cleaned_func:
            continue
        if cleaned_func.lower() in existing_names:
            try:
                response = requests.delete(f"{base_url}/crm-bot-functions/{existing_names[cleaned_func.lower()]}", headers=headers)
                response.raise_for_status()
                data = response.json()
                if data.get("status") == "SUCCESS":
                    result["deleted"].append(cleaned_func)
                else:
                    result["not_found"].append(cleaned_func)
            except requests.RequestException as e:
                result["not_found"].append(cleaned_func)
                logger.error(f"Error deleting function {cleaned_func}: {e}")
        else:
            result["not_found"].append(cleaned_func)
    return result

def sending_bot_functions_and_situation_block(
    last_assistant_message: str,
    user_message: str,
    categories: Optional[str],
    user_id: str,
    intent: str,
    x_api_key: str,
    base_url: str
) -> Dict[str, List[str]]:
    try:
        if intent == "update":
            return getting_bot_function_and_comparing(categories, user_id, x_api_key, base_url)
        elif intent == "delete":
            functions = json.loads(categories) if categories else [func["name"] for func in fetch_existing_categories(user_id, x_api_key, base_url)]
            return delete_bot_functions(functions, user_id, x_api_key, base_url)
        return {}
    except Exception as e:
        logger.error(f"Error in sending bot functions: {e}")
        return {}