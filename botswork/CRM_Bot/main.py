from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from openai import OpenAI
from dotenv import load_dotenv
import os
import json
import uvicorn
from models import ChatMessage, ChatResponse
from crm_chatbot import (
    load_prompts, fetch_existing_categories, is_list_categories_request, is_adding_function_request,
    is_deleting_function_request, is_confirmation_message, load_conversations, save_conversations, sending_bot_functions_and_situation_block
)
import logging

logging.basicConfig(level=logging.ERROR)
logger = logging.getLogger(__name__)

load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
X_API_KEY = os.getenv("X_API_KEY")
BASE_URL = os.getenv("BASE_URL")

if not all([OPENAI_API_KEY, X_API_KEY, BASE_URL]):
    raise ValueError("Missing required environment variables")

app = FastAPI()
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"])
client = OpenAI(api_key=OPENAI_API_KEY)
prompts_crm = load_prompts()
ORIGINAL_SYSTEM_PROMPT = prompts_crm.get("original_system_prompt", "")
CATEGORY_EXTRACTION_PROMPT = prompts_crm.get("category_extraction_prompt", "")
INTENT_DETECTION_PROMPT = prompts_crm.get("intent_detection_prompt", "")

def generate_response(conversation, user_message, state, categories=None, intent=None):
    """Generate a dynamic response using GPT-4o based on the conversation context."""
    try:
        conversation_history = "\n".join(f"{msg['role'].capitalize()}: {msg['content']}" for msg in conversation)
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": ORIGINAL_SYSTEM_PROMPT},
                {
                    "role": "user",
                    "content": (
                        f"Conversation history: {conversation_history}\n"
                        f"Current user message: {user_message}\n"
                        f"Current state: {state}\n"
                        f"Extracted categories: {categories if categories else 'None'}\n"
                        f"Intent: {intent if intent else 'None'}"
                    )
                }
            ],
            max_tokens=500,
            temperature=0.7
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        logger.error(f"Error generating response: {e}")
        return (
            "I’m sorry, I ran into an issue. I’m here to help with CRM functions—adding, deleting, or listing them. "
            "Please try again or let me know how I can assist!"
        )

@app.post("/chat", response_model=ChatResponse)
async def chat(message: ChatMessage):
    try:
        conversations = load_conversations()
        existing_functions = fetch_existing_categories(message.user_id, X_API_KEY, BASE_URL)
        categories_str = ", ".join(func["name"] for func in existing_functions) if existing_functions else "None"

        if message.user_id not in conversations:
            conversations[message.user_id] = [
                {"role": "system", "content": ORIGINAL_SYSTEM_PROMPT},
                {"role": "user", "content": message.message}
            ]
            assistant_message = generate_response(
                conversations[message.user_id], 
                message.message, 
                "initial", 
                categories=categories_str
            )
            conversations[message.user_id].append({
                "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
            })
            save_conversations(conversations)
            return ChatResponse(user_id=message.user_id, response=assistant_message)

        conversations[message.user_id].append({"role": "user", "content": message.message})

        if is_list_categories_request(message.message):
            assistant_message = generate_response(
                conversations[message.user_id], 
                message.message, 
                "initial", 
                categories=categories_str, 
                intent="list"
            )
            conversations[message.user_id].append({
                "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
            })
            save_conversations(conversations)
            return ChatResponse(user_id=message.user_id, response=assistant_message)

        last_message = conversations[message.user_id][-2] if len(conversations[message.user_id]) >= 2 else None
        last_state = last_message.get("state", "initial") if last_message else "initial"

        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": INTENT_DETECTION_PROMPT},
                    {"role": "user", "content": f"Assistant's last message: {last_message['content'] if last_message else ''}\nUser's current message: {message.message}"}
                ],
                max_tokens=10,
                temperature=0.5
            )
            intent = response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Error detecting intent: {e}")
            intent = None

        if last_state == "initial" and is_adding_function_request(message.message):
            try:
                response = client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": CATEGORY_EXTRACTION_PROMPT},
                        {"role": "user", "content": f"Extract function names from this user message: {message.message}"}
                    ],
                    max_tokens=100,
                    temperature=0.5
                )
                function_names = response.choices[0].message.content.strip()
                if function_names and function_names != "None" and function_names != "\"None\"":
                    parsed = json.loads(function_names) if function_names.startswith("[") else [function_names]
                    function_list = [name.strip().strip("'\"") for name in parsed if name and name.lower() not in ["none", "\"none\""]]
                    if not function_list:
                        assistant_message = generate_response(
                            conversations[message.user_id], 
                            message.message, 
                            "initial", 
                            categories=categories_str, 
                            intent="add"
                        )
                        conversations[message.user_id].append({
                            "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                        })
                        save_conversations(conversations)
                        return ChatResponse(user_id=message.user_id, response=assistant_message)

                    existing_names = [func["name"].lower() for func in existing_functions]
                    existing = [f for f in function_list if f.lower() in existing_names]
                    new_functions = [f for f in function_list if f.lower() not in existing_names]

                    if not new_functions:
                        assistant_message = generate_response(
                            conversations[message.user_id], 
                            message.message, 
                            "initial", 
                            categories=json.dumps(existing), 
                            intent="add"
                        )
                        conversations[message.user_id].append({
                            "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                        })
                        save_conversations(conversations)
                        return ChatResponse(user_id=message.user_id, response=assistant_message)

                    categories = {"functions": [{"name": func, "situation": None} for func in new_functions], "current_index": 0}
                    assistant_message = generate_response(
                        conversations[message.user_id], 
                        message.message, 
                        "awaiting_situation_individual", 
                        categories=json.dumps(categories), 
                        intent="situation"
                    )
                    conversations[message.user_id].append({
                        "role": "assistant", "content": assistant_message, "categories": json.dumps(categories), "state": "awaiting_situation_individual"
                    })
                    save_conversations(conversations)
                    return ChatResponse(user_id=message.user_id, response=assistant_message)
                else:
                    assistant_message = generate_response(
                        conversations[message.user_id], 
                        message.message, 
                        "initial", 
                        categories=categories_str, 
                        intent="add"
                    )
                    conversations[message.user_id].append({
                        "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                    })
                    save_conversations(conversations)
                    return ChatResponse(user_id=message.user_id, response=assistant_message)
            except Exception as e:
                logger.error(f"Error processing add request: {e}")
                assistant_message = generate_response(
                    conversations[message.user_id], 
                    message.message, 
                    "initial", 
                    categories=categories_str, 
                    intent="error"
                )
                conversations[message.user_id].append({
                    "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                })
                save_conversations(conversations)
                return ChatResponse(user_id=message.user_id, response=assistant_message)

        elif last_state == "initial" and is_deleting_function_request(message.message):
            try:
                response = client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": CATEGORY_EXTRACTION_PROMPT},
                        {"role": "user", "content": f"Extract function names from this user message: {message.message}"}
                    ],
                    max_tokens=100,
                    temperature=0.5
                )
                function_names = response.choices[0].message.content.strip()
                existing_names = [func["name"].lower() for func in existing_functions]

                if function_names and function_names != "None" and function_names != "\"None\"":
                    parsed = json.loads(function_names) if function_names.startswith("[") else [function_names]
                    function_list = [name.strip().strip("'\"") for name in parsed if name and name.lower() not in ["none", "\"none\""]]
                    if not function_list:
                        assistant_message = generate_response(
                            conversations[message.user_id], 
                            message.message, 
                            "initial", 
                            categories=categories_str, 
                            intent="delete"
                        )
                        conversations[message.user_id].append({
                            "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                        })
                        save_conversations(conversations)
                        return ChatResponse(user_id=message.user_id, response=assistant_message)

                    if not existing_functions:
                        assistant_message = generate_response(
                            conversations[message.user_id], 
                            message.message, 
                            "initial", 
                            categories=categories_str, 
                            intent="delete"
                        )
                        conversations[message.user_id].append({
                            "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                        })
                        save_conversations(conversations)
                        return ChatResponse(user_id=message.user_id, response=assistant_message)

                    valid_functions = [f for f in function_list if f.lower() in existing_names]
                    invalid_functions = [f for f in function_list if f.lower() not in existing_names]
                    if not valid_functions:
                        assistant_message = generate_response(
                            conversations[message.user_id], 
                            message.message, 
                            "initial", 
                            categories=json.dumps(function_list), 
                            intent="delete"
                        )
                        conversations[message.user_id].append({
                            "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                        })
                        save_conversations(conversations)
                        return ChatResponse(user_id=message.user_id, response=assistant_message)

                    assistant_message = generate_response(
                        conversations[message.user_id], 
                        message.message, 
                        "awaiting_deletion_confirmation", 
                        categories=json.dumps({"valid": valid_functions, "invalid": invalid_functions}), 
                        intent="delete"
                    )
                    conversations[message.user_id].append({
                        "role": "assistant", "content": assistant_message, "categories": json.dumps(valid_functions), "state": "awaiting_deletion_confirmation"
                    })
                    save_conversations(conversations)
                    return ChatResponse(user_id=message.user_id, response=assistant_message)
                else:
                    if not existing_functions:
                        assistant_message = generate_response(
                            conversations[message.user_id], 
                            message.message, 
                            "initial", 
                            categories=categories_str, 
                            intent="delete"
                        )
                        conversations[message.user_id].append({
                            "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                        })
                        save_conversations(conversations)
                        return ChatResponse(user_id=message.user_id, response=assistant_message)
                    assistant_message = generate_response(
                        conversations[message.user_id], 
                        message.message, 
                        "awaiting_deletion_confirmation", 
                        categories=json.dumps([func["name"] for func in existing_functions]), 
                        intent="delete"
                    )
                    conversations[message.user_id].append({
                        "role": "assistant", "content": assistant_message, "categories": json.dumps([func["name"] for func in existing_functions]),
                        "state": "awaiting_deletion_confirmation"
                    })
                    save_conversations(conversations)
                    return ChatResponse(user_id=message.user_id, response=assistant_message)
            except Exception as e:
                logger.error(f"Error processing delete request: {e}")
                assistant_message = generate_response(
                    conversations[message.user_id], 
                    message.message, 
                    "initial", 
                    categories=categories_str, 
                    intent="error"
                )
                conversations[message.user_id].append({
                    "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                })
                save_conversations(conversations)
                return ChatResponse(user_id=message.user_id, response=assistant_message)

        elif last_state == "awaiting_situation_individual":
            try:
                prev_categories = json.loads(conversations[message.user_id][-2]["categories"]) if conversations[message.user_id][-2]["categories"] else {}
                functions = prev_categories.get("functions", [])
                current_index = prev_categories.get("current_index", 0)
                if functions and current_index < len(functions):
                    situation_block = message.message.strip()
                    if not situation_block or len(situation_block.split()) < 3:
                        assistant_message = generate_response(
                            conversations[message.user_id], 
                            message.message, 
                            "awaiting_situation_individual", 
                            categories=json.dumps(prev_categories), 
                            intent="situation"
                        )
                        conversations[message.user_id].append({
                            "role": "assistant", "content": assistant_message, "categories": json.dumps(prev_categories), "state": "awaiting_situation_individual"
                        })
                        save_conversations(conversations)
                        return ChatResponse(user_id=message.user_id, response=assistant_message)
                    functions[current_index]["situation"] = situation_block
                    current_index += 1
                    if current_index < len(functions):
                        assistant_message = generate_response(
                            conversations[message.user_id], 
                            message.message, 
                            "awaiting_situation_individual", 
                            categories=json.dumps({"functions": functions, "current_index": current_index}), 
                            intent="situation"
                        )
                        conversations[message.user_id].append({
                            "role": "assistant", "content": assistant_message, "categories": json.dumps({"functions": functions, "current_index": current_index}),
                            "state": "awaiting_situation_individual"
                        })
                        save_conversations(conversations)
                        return ChatResponse(user_id=message.user_id, response=assistant_message)
                    else:
                        existing_names = [func["name"].lower() for func in existing_functions]
                        existing = [f["name"] for f in functions if f["name"].lower() in existing_names]
                        new_functions = [f for f in functions if f["name"].lower() not in existing_names]
                        assistant_message = generate_response(
                            conversations[message.user_id], 
                            message.message, 
                            "awaiting_confirmation", 
                            categories=json.dumps({"functions": new_functions, "existing": existing}), 
                            intent="update"
                        )
                        conversations[message.user_id].append({
                            "role": "assistant", "content": assistant_message, "categories": json.dumps({"functions": new_functions}), "state": "awaiting_confirmation"
                        })
                        save_conversations(conversations)
                        return ChatResponse(user_id=message.user_id, response=assistant_message)
                else:
                    assistant_message = generate_response(
                        conversations[message.user_id], 
                        message.message, 
                        "initial", 
                        categories=categories_str, 
                        intent="error"
                    )
                    conversations[message.user_id].append({
                        "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                    })
                    save_conversations(conversations)
                    return ChatResponse(user_id=message.user_id, response=assistant_message)
            except Exception as e:
                logger.error(f"Error processing situation: {e}")
                assistant_message = generate_response(
                    conversations[message.user_id], 
                    message.message, 
                    "initial", 
                    categories=categories_str, 
                    intent="error"
                )
                conversations[message.user_id].append({
                    "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                })
                save_conversations(conversations)
                return ChatResponse(user_id=message.user_id, response=assistant_message)

        elif last_state == "awaiting_confirmation" and is_confirmation_message(message.message):
            try:
                prev_categories = json.loads(conversations[message.user_id][-2]["categories"]) if conversations[message.user_id][-2]["categories"] else {}
                functions = prev_categories.get("functions", [])
                if not functions:
                    assistant_message = generate_response(
                        conversations[message.user_id], 
                        message.message, 
                        "initial", 
                        categories=categories_str, 
                        intent="error"
                    )
                    conversations[message.user_id].append({
                        "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                    })
                    save_conversations(conversations)
                    return ChatResponse(user_id=message.user_id, response=assistant_message)

                categories_dict = {func["name"]: func["situation"] for func in functions if func["situation"]}
                categories = json.dumps(categories_dict) if categories_dict else None
                if not categories:
                    logger.error("No valid categories to add")
                    assistant_message = generate_response(
                        conversations[message.user_id], 
                        message.message, 
                        "initial", 
                        categories=categories_str, 
                        intent="error"
                    )
                    conversations[message.user_id].append({
                        "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                    })
                    save_conversations(conversations)
                    return ChatResponse(user_id=message.user_id, response=assistant_message)

                result = sending_bot_functions_and_situation_block(
                    conversations[message.user_id][-2]["content"], 
                    message.message, 
                    categories, 
                    message.user_id, 
                    "update", 
                    X_API_KEY, 
                    BASE_URL
                )
                existing = result.get("existing", [])
                added = result.get("added", [])
                failed = result.get("failed", [])

                if failed:
                    assistant_message = f"Failed to add {', '.join(failed)} due to an issue. "
                    if added:
                        assistant_message += f"Successfully added {', '.join(added)}. "
                    if existing:
                        assistant_message += f"Note: {', '.join(existing)} already exist and were not added again. "
                    assistant_message += "Let me know how else I can help."
                elif added:
                    assistant_message = f"Successfully added {', '.join(added)}! "
                    if existing:
                        assistant_message += f"Note: {', '.join(existing)} already exist and were not added again. "
                    assistant_message += "Let me know how else I can help."
                else:
                    assistant_message = f"Note: {', '.join(existing)} already exist and were not added again. Let me know how else I can help."

                updated_functions = fetch_existing_categories(message.user_id, X_API_KEY, BASE_URL)
                updated_categories_str = ", ".join(func["name"] for func in updated_functions) if updated_functions else "None"
                conversations[message.user_id].append({
                    "role": "assistant", "content": assistant_message, "categories": updated_categories_str, "state": "initial"
                })
                save_conversations(conversations)
                return ChatResponse(user_id=message.user_id, response=assistant_message)
            except Exception as e:
                logger.error(f"Error adding functions: {e}")
                assistant_message = generate_response(
                    conversations[message.user_id], 
                    message.message, 
                    "initial", 
                    categories=categories_str, 
                    intent="error"
                )
                conversations[message.user_id].append({
                    "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                })
                save_conversations(conversations)
                return ChatResponse(user_id=message.user_id, response=assistant_message)

        elif last_state == "awaiting_deletion_confirmation" and is_confirmation_message(message.message):
            try:
                prev_categories = json.loads(conversations[message.user_id][-2]["categories"]) if conversations[message.user_id][-2]["categories"] else []
                if prev_categories:
                    result = sending_bot_functions_and_situation_block(
                        conversations[message.user_id][-2]["content"], 
                        message.message, 
                        json.dumps(prev_categories), 
                        message.user_id, 
                        "delete", 
                        X_API_KEY, 
                        BASE_URL
                    )
                    deleted = result.get("deleted", [])
                    not_found = result.get("not_found", [])
                    assistant_message = generate_response(
                        conversations[message.user_id], 
                        message.message, 
                        "initial", 
                        categories=json.dumps({"deleted": deleted, "not_found": not_found}), 
                        intent="delete"
                    )
                    updated_functions = fetch_existing_categories(message.user_id, X_API_KEY, BASE_URL)
                    conversations[message.user_id].append({
                        "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                    })
                    save_conversations(conversations)
                    return ChatResponse(user_id=message.user_id, response=assistant_message)
                else:
                    assistant_message = generate_response(
                        conversations[message.user_id], 
                        message.message, 
                        "initial", 
                        categories=categories_str, 
                        intent="error"
                    )
                    conversations[message.user_id].append({
                        "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                    })
                    save_conversations(conversations)
                    return ChatResponse(user_id=message.user_id, response=assistant_message)
            except Exception as e:
                logger.error(f"Error deleting functions: {e}")
                assistant_message = generate_response(
                    conversations[message.user_id], 
                    message.message, 
                    "initial", 
                    categories=categories_str, 
                    intent="error"
                )
                conversations[message.user_id].append({
                    "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
                })
                save_conversations(conversations)
                return ChatResponse(user_id=message.user_id, response=assistant_message)

        assistant_message = generate_response(
            conversations[message.user_id], 
            message.message, 
            "initial", 
            categories=categories_str, 
            intent="error"
        )
        conversations[message.user_id].append({
            "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
        })
        save_conversations(conversations)
        return ChatResponse(user_id=message.user_id, response=assistant_message)

    except Exception as e:
        logger.error(f"Error processing request: {e}")
        assistant_message = generate_response(
            conversations.get(message.user_id, []), 
            message.message, 
            "initial", 
            categories=categories_str, 
            intent="error"
        )
        if message.user_id in conversations:
            conversations[message.user_id].append({
                "role": "assistant", "content": assistant_message, "categories": categories_str, "state": "initial"
            })
            save_conversations(conversations)
        return ChatResponse(user_id=message.user_id, response=assistant_message)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
