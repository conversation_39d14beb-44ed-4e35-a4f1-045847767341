import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import httpx
import os
from dotenv import load_dotenv
from openai import Async<PERSON>penA<PERSON>

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
X_API_KEY = os.getenv("X_API_KEY")
BASE_URL = os.getenv("BASE_URL")

# Initialize OpenAI client
client = AsyncOpenAI(api_key=OPENAI_API_KEY)

# Load prompts from JSON file
try:
    with open("email_prompts.json", "r") as file:
        prompts = json.load(file)
        SYSTEM_PROMPT = prompts["system_prompt"]
        EMAIL_EXTRACTION_PROMPT = prompts["email_extraction_prompt"]
except FileNotFoundError:
    logger.error("email_prompts.json file not found")
    raise Exception("Could not load email_prompts.json. Please ensure the file exists in the correct directory.")
except KeyError as e:
    logger.error(f"Missing key in email_prompts.json: {e}")
    raise Exception(f"Invalid email_prompts.json format. Ensure it contains 'system_prompt' and 'email_extraction_prompt' keys.")
except json.JSONDecodeError as e:
    logger.error(f"Invalid JSON in email_prompts.json: {e}")
    raise Exception("Invalid JSON format in email_prompts.json.")

# Cache for tokens and requests
_token_cache = {}
_request_cache = {}

async def fetch_bearer_token(user_id: str) -> Optional[Dict]:
    """Fetch bearer token and userName from the auth/user/token API."""
    if user_id in _token_cache:
        return _token_cache[user_id]
    
    url = f"{BASE_URL}/auth/user/token?userId={user_id}"
    headers = {"accept": "*/*", "X-API-Key": X_API_KEY}
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, headers=headers, timeout=10.0)
            response.raise_for_status()
            data = response.json()
            
            if data.get("status") == "SUCCESS" and isinstance(data.get("result"), dict):
                token = data["result"].get("token")
                user_name = data["result"].get("userName", "User")
                if not token:
                    logger.error(f"No token found in API response for user_id {user_id}")
                    return None
                    
                auth_data = {"token": token, "user_name": user_name}
                _token_cache[user_id] = auth_data
                return auth_data
                
            logger.error(f"Unexpected response format from token API for user_id {user_id}")
            return None
            
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching bearer token for user {user_id}: {e}")
            return None
        except httpx.RequestError as e:
            logger.error(f"Request error fetching bearer token for user {user_id}: {e}")
            return None

async def validate_user_registration(user_id: str) -> Dict:
    """Validate user_id registration"""
    auth_data = await fetch_bearer_token(user_id)
    if not auth_data or not auth_data.get("token"):
        return {"valid": False, "error": "Failed to fetch valid token"}
    return {"valid": True, "user_name": auth_data.get("user_name", "User")}

async def list_email_functions_tool(user_id: str) -> Dict:
    """List existing email bot functions and their configurations with parallel detail fetching"""
    try:
        auth_data = await fetch_bearer_token(user_id)
        if not auth_data:
            return {"error": "Authentication failed"}
        
        url = f"{BASE_URL}/email-bot-functions/list"
        headers = {
            "Authorization": f"Bearer {auth_data['token']}",
            "accept": "*/*"
        }
        
        async with httpx.AsyncClient() as client:
            # First, get the main list
            response = await client.get(url, headers=headers, timeout=10.0)
            response.raise_for_status()
            data = response.json()
            
            if data.get("status") != "SUCCESS" or "result" not in data:
                return {"error": "Failed to fetch email bot functions", "details": data}
            
            # Prepare function data structure
            functions = []
            interpretation_tasks = []
            function_mapping = {}  # Maps interpretation_id to function index
            
            for func_idx, item in enumerate(data["result"]):
                function_info = {
                    "name": item["name"],
                    "id": item["_id"],
                    "interpretations": item.get("interpretations", []),
                    "situations": []
                }
                functions.append(function_info)
                
                # Create tasks for each interpretation
                for interpretation in item.get("interpretations", []):
                    interp_id = interpretation['_id']
                    function_mapping[interp_id] = func_idx
                    
                    task = fetch_interpretation_details(client, headers, interp_id)
                    interpretation_tasks.append((interp_id, task))
            
            # Execute all detail requests in parallel
            if interpretation_tasks:
                # Extract task IDs and tasks
                task_ids, tasks = zip(*interpretation_tasks)
                
                # Run all tasks in parallel
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results and map back to functions
                for interp_id, result in zip(task_ids, results):
                    func_idx = function_mapping[interp_id]
                    
                    if isinstance(result, Exception):
                        logger.warning(f"Failed to get details for interpretation {interp_id}: {result}")
                        continue
                    
                    if result:  # If we got situations back
                        functions[func_idx]["situations"].extend(result)
            
            return {
                "functions": functions,
                "count": len(functions)
            }
            
    except Exception as e:
        logger.error(f"Error in list_email_functions_tool: {e}")
        return {"error": str(e)}

async def fetch_interpretation_details(client: httpx.AsyncClient, headers: dict, interpretation_id: str) -> List[str]:
    """Fetch details for a single interpretation"""
    try:
        detail_url = f"{BASE_URL}/email-bot-function-detailed?interpretationId={interpretation_id}"
        detail_response = await client.get(detail_url, headers=headers, timeout=10.0)
        
        if detail_response.status_code == 200:
            detail_data = detail_response.json()
            if detail_data.get("status") == "SUCCESS" and "result" in detail_data:
                situations = detail_data["result"].get("situation", [])
                return [s["name"] for s in situations]
        
        return []
        
    except Exception as e:
        logger.warning(f"Failed to get details for interpretation {interpretation_id}: {e}")
        return []

async def list_email_functions_tool_with_limit(user_id: str, max_concurrent: int = 10) -> Dict:
    """Version with concurrency limiting to avoid overwhelming the server"""
    try:
        auth_data = await fetch_bearer_token(user_id)
        if not auth_data:
            return {"error": "Authentication failed"}
        
        url = f"{BASE_URL}/email-bot-functions/list"
        headers = {
            "Authorization": f"Bearer {auth_data['token']}",
            "accept": "*/*"
        }
        
        # Create semaphore for limiting concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async with httpx.AsyncClient() as client:
            # Get main list
            response = await client.get(url, headers=headers, timeout=10.0)
            response.raise_for_status()
            data = response.json()
            
            if data.get("status") != "SUCCESS" or "result" not in data:
                return {"error": "Failed to fetch email bot functions", "details": data}
            
            functions = []
            interpretation_tasks = []
            function_mapping = {}
            
            for func_idx, item in enumerate(data["result"]):
                function_info = {
                    "name": item["name"],
                    "id": item["_id"],
                    "interpretations": item.get("interpretations", []),
                    "situations": []
                }
                functions.append(function_info)
                
                # Create limited tasks for each interpretation
                for interpretation in item.get("interpretations", []):
                    interp_id = interpretation['_id']
                    function_mapping[interp_id] = func_idx
                    
                    task = fetch_interpretation_details_limited(client, headers, interp_id, semaphore)
                    interpretation_tasks.append((interp_id, task))
            
            # Execute all tasks in parallel (but limited by semaphore)
            if interpretation_tasks:
                task_ids, tasks = zip(*interpretation_tasks)
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for interp_id, result in zip(task_ids, results):
                    func_idx = function_mapping[interp_id]
                    
                    if isinstance(result, Exception):
                        logger.warning(f"Failed to get details for interpretation {interp_id}: {result}")
                        continue
                    
                    if result:
                        functions[func_idx]["situations"].extend(result)
            
            return {
                "functions": functions,
                "count": len(functions)
            }
            
    except Exception as e:
        logger.error(f"Error in list_email_functions_tool_with_limit: {e}")
        return {"error": str(e)}

async def fetch_interpretation_details_limited(client: httpx.AsyncClient, headers: dict, 
                                             interpretation_id: str, semaphore: asyncio.Semaphore) -> List[str]:
    """Fetch details for a single interpretation with concurrency limiting"""
    async with semaphore:
        return await fetch_interpretation_details(client, headers, interpretation_id)

async def create_email_function_tool(user_id: str, function_name: str, situation_description: str) -> Dict:
    """Create a new email bot function with situations for filtering emails"""
    try:
        # Check if the function already exists
        existing_functions = await list_email_functions_tool(user_id)
        if "error" in existing_functions:
            return {"error": f"Failed to check existing functions: {existing_functions['error']}"}
        
        clean_name = function_name.strip()
        clean_value = clean_name.lower().replace(" ", "_").replace("-", "_")
        
        # Check for duplicate function name
        for func in existing_functions.get("functions", []):
            if func["name"].lower() == clean_name.lower():
                return {
                    "error": f"Bot function '{clean_name}' already exists",
                    "suggestion": f"Would you like to add the situation '{situation_description}' to the existing '{clean_name}' function instead?"
                }
        
        auth_data = await fetch_bearer_token(user_id)
        if not auth_data:
            return {"error": "Authentication failed"}
        
        headers = {
            "Authorization": f"Bearer {auth_data['token']}",
            "Content-Type": "application/json",
            "accept": "*/*"
        }
        
        async with httpx.AsyncClient() as client:
            # 1. Create the email bot function
            function_payload = {"value": clean_value, "name": clean_name}
            response = await client.post(f"{BASE_URL}/email-bot-functions", headers=headers, json=function_payload, timeout=10.0)
            response.raise_for_status()
            function_data = response.json()
            
            if function_data.get("status") != "SUCCESS":
                return {"error": "Failed to create email bot function", "details": function_data}
            
            # 2. Create interpretation
            interpretation_payload = {"botFunctionsValue": clean_value, "value": clean_value, "name": clean_name}
            response = await client.post(f"{BASE_URL}/email-bot-functions/interpretation", headers=headers, json=interpretation_payload, timeout=10.0)
            response.raise_for_status()
            interpretation_data = response.json()
            
            if interpretation_data.get("status") != "SUCCESS":
                return {"error": "Failed to create email interpretation", "details": interpretation_data}
            
            interpretation_id = interpretation_data["result"]["_id"]
            
            # 3. Get detailed function ID
            response = await client.get(f"{BASE_URL}/email-bot-function-detailed?interpretationId={interpretation_id}", headers=headers, timeout=10.0)
            response.raise_for_status()
            detailed_data = response.json()
            
            if detailed_data.get("status") != "SUCCESS":
                return {"error": "Failed to get detailed email bot function", "details": detailed_data}
            
            detailed_id = detailed_data["result"]["_id"]
            
            # 4. Add the new situation
            situation_payload = {"botFunctionDetailedId": detailed_id, "name": situation_description}
            response = await client.post(f"{BASE_URL}/email-bot-function-detailed/situation", headers=headers, json=situation_payload, timeout=10.0)
            response.raise_for_status()
            situation_data = response.json()
            
            if situation_data.get("status") != "SUCCESS":
                return {"error": "Failed to add email situation", "details": situation_data}
            
            return {
                "success": True,
                "function_name": function_name,
                "new_situation": situation_description
            }
            
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error in create_email_function_tool: {e}")
        if e.response.status_code == 400:
            return {"error": f"Failed to create bot function '{clean_name}' - it may already exist", "details": e.response.json()}
        return {"error": f"HTTP error: {str(e)}"}
    except Exception as e:
        logger.error(f"Error in create_email_function_tool: {e}")
        return {"error": str(e)}

async def add_email_situation_tool(user_id: str, function_name: str, situation_description: str) -> Dict:
    """Add a new situation to an existing email bot function"""
    try:
        # First, get the function details
        functions_result = await list_email_functions_tool(user_id)
        if "error" in functions_result:
            return functions_result
        
        # Find the matching function
        target_function = None
        for func in functions_result["functions"]:
            if func["name"].lower() == function_name.lower():
                target_function = func
                break
        
        if not target_function:
            return {"error": f"email bot function '{function_name}' not found"}
        
        if not target_function["interpretations"]:
            return {"error": f"No interpretations found for email bot function '{function_name}'"}
        
        auth_data = await fetch_bearer_token(user_id)
        if not auth_data:
            return {"error": "Authentication failed"}
        
        headers = {
            "Authorization": f"Bearer {auth_data['token']}",
            "Content-Type": "application/json",
            "accept": "*/*"
        }
        
        # Get detailed function ID
        interpretation_id = target_function["interpretations"][0]["_id"]
        
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/email-bot-function-detailed?interpretationId={interpretation_id}", headers=headers, timeout=10.0)
            response.raise_for_status()
            detailed_data = response.json()
            
            if detailed_data.get("status") != "SUCCESS":
                return {"error": "Failed to get detailed email bot function", "details": detailed_data}
            
            detailed_id = detailed_data["result"]["_id"]
            
            # Add the new situation
            situation_payload = {"botFunctionDetailedId": detailed_id, "name": situation_description}
            response = await client.post(f"{BASE_URL}/email-bot-function-detailed/situation", headers=headers, json=situation_payload, timeout=10.0)
            response.raise_for_status()
            situation_data = response.json()
            
            if situation_data.get("status") != "SUCCESS":
                return {"error": "Failed to add email situation", "details": situation_data}
            
            return {
                "success": True,
                "function_name": function_name,
                "new_situation": situation_description
            }
            
    except Exception as e:
        logger.error(f"Error in add_email_situation_tool: {e}")
        return {"error": str(e)}

async def update_email_situation_tool(user_id: str, function_name: str, old_situation: str, new_situation: str) -> Dict:
    """Update an existing situation for an email bot function"""
    try:
        # Get function details
        functions_result = await list_email_functions_tool(user_id)
        if "error" in functions_result:
            return functions_result
        
        # Find the matching function
        target_function = None
        for func in functions_result["functions"]:
            if func["name"].lower() == function_name.lower():
                target_function = func
                break
        
        if not target_function:
            return {"error": f"email bot function '{function_name}' not found"}
        
        if not target_function["interpretations"]:
            return {"error": f"No interpretations found for email bot function '{function_name}'"}
        
        auth_data = await fetch_bearer_token(user_id)
        if not auth_data:
            return {"error": "Authentication failed"}
        
        headers = {
            "Authorization": f"Bearer {auth_data['token']}",
            "Content-Type": "application/json",
            "accept": "*/*"
        }
        
        interpretation_id = target_function["interpretations"][0]["_id"]
        
        async with httpx.AsyncClient() as client:
            # Get detailed function info with situations
            response = await client.get(f"{BASE_URL}/email-bot-function-detailed?interpretationId={interpretation_id}", headers=headers, timeout=10.0)
            response.raise_for_status()
            detailed_data = response.json()
            
            if detailed_data.get("status") != "SUCCESS":
                return {"error": "Failed to get detailed email bot function", "details": detailed_data}
            
            detailed_id = detailed_data["result"]["_id"]
            situations = detailed_data["result"].get("situation", [])
            
            # Find the situation to update
            target_situation = None
            for situation in situations:
                if situation["name"].lower() == old_situation.lower():
                    target_situation = situation
                    break
            
            if not target_situation:
                return {"error": f"Situation '{old_situation}' not found in function '{function_name}'"}
            
            situation_id = target_situation['id']
            
            # Update the situation with correct payload
            update_payload = {
                "botFunctionDetailedId": detailed_id,
                "situationId": situation_id,
                "name": new_situation
            }
            response = await client.put(f"{BASE_URL}/email-bot-function-detailed/situation", headers=headers, json=update_payload, timeout=10.0)
            response.raise_for_status()
            update_data = response.json()
            
            if update_data.get("status") != "SUCCESS":
                return {"error": "Failed to update email situation", "details": update_data}
            
            return {
                "success": True,
                "function_name": function_name,
                "old_situation": old_situation,
                "new_situation": new_situation
            }
            
    except Exception as e:
        logger.error(f"Error in update_email_situation_tool: {e}")
        return {"error": str(e)}

async def send_email_to_api(to: str, subject: str, content: str, user_id: str) -> Dict:
    """Send email details to the mail API with HTML formatting."""
    auth_data = await fetch_bearer_token(user_id)
    if not auth_data or not auth_data.get("token"):
        logger.error(f"No valid bearer token for user_id {user_id}, cannot send email")
        return {"error": "Authentication failed"}
    
    # Convert newlines to <br> tags for HTML rendering
    html_content = content.replace("\n", "<br>")
    
    # Wrap content in basic HTML structure
    html_content = f"""
    <html>
        <body>
            {html_content}
        </body>
    </html>
    """
    
    url = f"{BASE_URL}/mail"
    headers = {
        "Authorization": f"Bearer {auth_data['token']}",
        "accept": "*/*"
    }
    data = {
        "toAddress": to,
        "subject": subject,
        "content": html_content,
        "mode": "html",  # Specify HTML mode
        "replyTo": "",
        "attachments": "",
        "bccAddress": "",
        "ccAddress": "",
        "action": "",
        "askReceipt": ""
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, headers=headers, files={k: (None, v) for k, v in data.items()}, timeout=10.0)
            response.raise_for_status()
            response_data = response.json()
            return {"success": True, "details": response_data}
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error sending email to {to} for user_id {user_id}: {e}")
            return {"error": f"Failed to send email: {str(e)}", "details": e.response.json() if e.response else {}}
        except httpx.RequestError as e:
            logger.error(f"Request error sending email to {to} for user_id {user_id}: {e}")
            return {"error": f"Failed to send email: {str(e)}"}

async def send_email_to_api_tool(draft_message: str, user_id: str) -> Dict:
    """Extract email details and send to mail API."""
    email_details = await extract_email_details(draft_message)
    if email_details:
        return await send_email_to_api(
            to=email_details["to"],
            subject=email_details["subject"],
            content=email_details["content"],
            user_id=user_id
        )
    else:
        logger.error(f"Failed to extract valid email details for user_id {user_id} from draft: {draft_message}")
        return {"error": "Failed to extract valid email details"}

async def extract_email_details(draft_message: str, max_retries: int = 5, retry_delay: float = 1.0) -> Optional[dict]:
    """Extract to, subject, and content from the email draft using OpenAI API."""
    attempt = 1
    while attempt <= max_retries:
        try:
            response = await client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": EMAIL_EXTRACTION_PROMPT.replace("{{draft_message}}", draft_message)},
                    {"role": "user", "content": draft_message}
                ],
                max_tokens=500,
                temperature=0.5
            )
            result = response.choices[0].message.content.strip()
            parsed = json.loads(result)
            if all(key in parsed for key in ["to", "subject", "content"]):
                return parsed
            else:
                logger.error(f"Attempt {attempt}: Missing required fields in email extraction response: {result}")
        except json.JSONDecodeError as e:
            logger.error(f"Attempt {attempt}: Error parsing email extraction JSON: {str(e)} - Response: {result}")
        except Exception as e:
            logger.error(f"Attempt {attempt}: Unknown error in email extraction: {str(e)}")
        
        if attempt == max_retries:
            logger.error(f"Max retries ({max_retries}) reached for email extraction. Returning None.")
            return None
        attempt += 1
        await asyncio.sleep(retry_delay)
    
    return None