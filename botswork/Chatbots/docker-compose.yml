version: "3.9"  # Docker Compose file format version

services:
  api:
    # Production service
    image: aicoe24/chatbotv2:v1.0.5   # 👈 your custom image name
    build:
      context: .
      dockerfile: Dockerfile
    container_name: Chatbots
    ports:
      - "8001:8000"
    command: >
      python main.py
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "python -c \"import socket,sys; s=socket.socket(); s.settimeout(2); s.connect(('127.0.0.1',8001)); s.close()\""]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
