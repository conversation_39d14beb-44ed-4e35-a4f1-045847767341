from fastapi import HTTPException, BackgroundTasks
from pydantic import BaseModel
from openai import AsyncOpenAI
import os
from dotenv import load_dotenv
import httpx
import json
from models import PostRequest,PostResponse
load_dotenv()

client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))

PEXELS_API_KEY = os.getenv("PEXELS_API_KEY")

if not PEXELS_API_KEY:
    raise ValueError("PEXELS_API_KEY is not set in the .env file or environment variables")
if not os.getenv("OPENAI_API_KEY"):
    raise ValueError("OPENAI_API_KEY is not set in the .env file or environment variables")


with open("social_media_post_prompts.json", "r", encoding="utf-8") as f:
    prompts = json.load(f)
    SOCIAL_MEDIA_PROMPT = prompts["social_media_prompt"]



async def generate_social_media_post_logic(request: PostRequest, background_tasks: BackgroundTasks):
    try:
        openai_prompt = SOCIAL_MEDIA_PROMPT.format(prompt=request.prompt)

        openai_response = await client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a social media content creator."},
                {"role": "user", "content": openai_prompt}
            ],
            response_format={"type": "json_object"}
        )

        openai_result = json.loads(openai_response.choices[0].message.content)

        async with httpx.AsyncClient() as http_client:
            pexels_response = await http_client.get(
                "https://api.pexels.com/v1/search",
                headers={"Authorization": PEXELS_API_KEY},
                params={"query": request.prompt, "per_page": 1}
            )

        if pexels_response.status_code != 200:
            raise HTTPException(
                status_code=500,
                detail=f"Pexels API error: {pexels_response.status_code} - {pexels_response.text}"
            )

        pexels_data = pexels_response.json()
        # Extract the first image URL (original size) or use a fallback if no images are found
        image_url = (
            pexels_data["photos"][0]["src"]["original"]
            if pexels_data.get("photos")
            else "https://www.pexels.com/assets/images/pexels-photo.jpg"  # Fallback image
        )


        return PostResponse(
            content=openai_result["content"],
            hashtags=openai_result["hashtags"],
            image_url=image_url
        )

    except json.JSONDecodeError:
        raise HTTPException(status_code=500, detail="Error parsing OpenAI response")
    except httpx.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Pexels API request failed: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating post or fetching image: {str(e)}")