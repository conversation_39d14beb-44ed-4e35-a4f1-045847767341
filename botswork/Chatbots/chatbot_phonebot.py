# chatbot_phonebot.py
from openai import OpenAI
import os
import json
from dotenv import load_dotenv
from typing import Optional, List, Dict
import logging
import aiohttp
import asyncio
import functools
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
X_API_KEY = os.getenv("X_API_KEY")
BASE_URL = os.getenv("BASE_URL")

if not all([OPENAI_API_KEY, X_API_KEY, BASE_URL]):
    logger.error("Missing required environment variables")
    raise ValueError("Missing required environment variables")

async def fetch_bearer_token_phone(user_id: str) -> Optional[str]:
    """Fetch bearer token asynchronously."""
    url = f"{BASE_URL}/auth/user/token?userId={user_id}"
    headers = {"accept": "*/*", "X-API-Key": X_API_KEY}
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(url, headers=headers) as response:
                response.raise_for_status()
                data = await response.json()
                if data.get("status") == "SUCCESS" and "result" in data:
                    return data["result"]
                logger.error(f"Unexpected response format for user_id {user_id}")
                return None
        except aiohttp.ClientError as e:
            logger.error(f"Error fetching bearer token for user_id {user_id}: {str(e)}")
            return None

async def fetch_all_bot_functions_phone(bearer_token: str) -> List[Dict]:
    """Fetch all bot functions and interpretations asynchronously."""
    url = f"{BASE_URL}/phone-bot-functions/list"
    headers = {"Authorization": f"Bearer {bearer_token}", "accept": "*/*"}
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(url, headers=headers) as response:
                response.raise_for_status()
                data = await response.json()
                if data.get("status") == "SUCCESS" and "result" in data:
                    return data["result"]
                logger.error("Unexpected response format from bot-functions/list API")
                return []
        except aiohttp.ClientError as e:
            logger.error(f"Error fetching bot functions list: {str(e)}")
            return []

async def fetch_bot_function_detailed_phone(interpretation_id: str, bearer_token: str) -> Optional[Dict]:
    """Fetch detailed bot function asynchronously."""
    url = f"{BASE_URL}/phone-bot-function-detailed?interpretationId={interpretation_id}"
    headers = {"Authorization": f"Bearer {bearer_token}", "accept": "*/*"}
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(url, headers=headers) as response:
                response.raise_for_status()
                data = await response.json()
                if data.get("status") == "SUCCESS" and "result" in data:
                    return data["result"]
                logger.error(f"Unexpected response format for {interpretation_id}")
                return None
        except aiohttp.ClientError as e:
            logger.error(f"Error fetching detailed bot function for {interpretation_id}: {str(e)}")
            return None

async def update_situation_block_phone(bot_function_detailed_id: str, situation_id: str, new_name: str, bearer_token: str) -> bool:
    """Update situation block asynchronously."""
    url = f"{BASE_URL}/phone-bot-function-detailed/situation"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
        "accept": "*/*"
    }
    payload = {
        "botFunctionDetailedId": bot_function_detailed_id,
        "situationId": situation_id,
        "name": new_name
    }
    async with aiohttp.ClientSession() as session:
        try:
            logger.info(f"Updating situation block for ID: {bot_function_detailed_id}")
            async with session.put(url, headers=headers, json=payload) as response:
                response.raise_for_status()
                logger.info(f"Situation block updated successfully")
                return True
        except aiohttp.ClientError as e:
            logger.error(f"Failed to update situation block: {str(e)}")
            return False

async def update_prompt_block_phone(bot_function_detailed_id: str, new_message: str, new_logic: str, bearer_token: str) -> bool:
    """Update prompt block asynchronously."""
    url = f"{BASE_URL}/phone-bot-function-detailed/prompt-block"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
        "accept": "*/*"
    }
    payload = {
        "botFunctionDetailedId": bot_function_detailed_id,
        "message": new_message,
        "logic": new_logic
    }
    async with aiohttp.ClientSession() as session:
        try:
            logger.info(f"Updating prompt block for ID: {bot_function_detailed_id}")
            logger.info(f"Payload: {payload}")
            async with session.put(url, headers=headers, json=payload) as response:
                response.raise_for_status()
                response_data = await response.json()
                logger.info(f"Prompt block updated successfully: {response_data}")
                return True
        except aiohttp.ClientError as e:
            logger.error(f"Failed to update prompt block: {str(e)}")
            try:
                error_text = await e.response.text() if hasattr(e, 'response') else str(e)
                logger.error(f"Error response: {error_text}")
            except:
                pass
            return False

def is_list_interpretations_request_phone(message: str) -> bool:
    """Check if user wants to list interpretations (optimized for speed)."""
    message_lower = message.lower().strip()
    list_keywords = {"list", "show", "view", "current", "existing", "interpretations", "functions", "display"}
    return any(keyword in message_lower for keyword in list_keywords) and not any(edit_word in message_lower for edit_word in {"edit", "update", "change", "modify"})

def is_greeting_message_phone(message: str) -> bool:
    """Check if the message is a simple greeting."""
    message_lower = message.lower().strip()
    greetings = {"hi", "hello", "hey", "greetings", "start", "begin"}
    return message_lower in greetings or message_lower.startswith(("hi ", "hello ", "hey "))

def is_edit_request_phone(message: str) -> bool:
    """Check if user wants to edit an interpretation."""
    message_lower = message.lower()
    edit_keywords = {"edit", "modify", "update", "change"}
    return any(keyword in message_lower for keyword in edit_keywords)

def is_create_request_phone(message: str) -> bool:
    """Check if user wants to create something new."""
    message_lower = message.lower()
    create_keywords = {"create", "add", "new", "make", "build", "setup", "set up"}
    return any(keyword in message_lower for keyword in create_keywords)

def find_interpretation_by_name_phone(bot_functions: List[Dict], interpretation_name: str) -> Optional[Dict]:
    """Find interpretation by name across all functions with fuzzy matching."""
    interpretation_name_lower = interpretation_name.lower().strip()
    
    # First try exact match
    for func in bot_functions:
        for interp in func.get("interpretations", []):
            if interp.get("name", "").lower() == interpretation_name_lower:
                return {
                    "function_name": func["name"],
                    "function_id": func["_id"],
                    "interpretation": interp
                }
    
    # Then try partial match
    for func in bot_functions:
        for interp in func.get("interpretations", []):
            interp_name = interp.get("name", "").lower()
            if interpretation_name_lower in interp_name or interp_name in interpretation_name_lower:
                return {
                    "function_name": func["name"],
                    "function_id": func["_id"],
                    "interpretation": interp
                }
    
    return None

@functools.lru_cache(maxsize=1)
def load_prompts_phone() -> dict:
    """Load prompts from JSON file (cached)."""
    try:
        with open("prompts_phonebot.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error("prompts_phonebot.json not found")
        raise
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing prompts_phonebot.json: {str(e)}")
        raise

prompts = load_prompts_phone()
BASE_SYSTEM_PROMPTS = prompts.get("base_system_prompts", "")
DATA_EXTRACTION_PROMPT = prompts.get("data_extraction_prompt", "")
INTENT_DETECTION_PROMPT = prompts.get("intent_detection_prompt", "")
MESSAGE_BLOCK_PROMPT = prompts.get("message_block_prompt", "")
BOT_FUNCTION_PROMPT = prompts.get("bot_function_prompt", "")
WELCOME_GENERATION_PROMPT = prompts.get("welcome_generation_prompt", "")
COMPLETION_PROMPT = prompts.get("completion_prompt", "")
ERROR_HANDLING_PROMPT = prompts.get("error_handling_prompt", "")
SINGLE_FUNCTION_EXTRACTION_PROMPT = prompts.get("single_function_extraction_prompt", "")

client = OpenAI(api_key=OPENAI_API_KEY)

CONVERSATIONS_FILE = "conversations.json"

class ConversationStatePhone:
    INITIAL = "initial"
    COLLECTING_MESSAGE_BLOCK = "collecting_message_block"
    COLLECTING_BOT_FUNCTIONS = "collecting_bot_functions"
    WAITING_FUNCTION_CONFIRMATION = "waiting_function_confirmation"
    MESSAGE_BLOCK_POSTED = "message_block_posted"
    BOT_FUNCTION_POSTED = "bot_function_posted"
    COMPLETED = "completed"
    LISTING_INTERPRETATIONS = "listing_interpretations"
    SELECTING_EDIT_TYPE = "selecting_edit_type"
    SELECTING_INTERPRETATION = "selecting_interpretation"
    COLLECTING_SITUATION_UPDATE = "collecting_situation_update"
    COLLECTING_PROMPT_UPDATE = "collecting_prompt_update"

def get_conversation_state_phone(conversations: dict, user_id: str) -> str:
    """Get current conversation state (optimized)."""
    user_conv = conversations.get(user_id, [])
    if user_conv and isinstance(user_conv[-1], dict):
        return user_conv[-1].get("state", ConversationStatePhone.INITIAL)
    return ConversationStatePhone.INITIAL

def get_latest_extracted_data_phone(conversation: list) -> Optional[dict]:
    """Get latest extracted data (optimized for last message)."""
    for message in conversation[-3::-1]:  # Look at last 3 messages
        if message.get("role") == "assistant" and message.get("extracted_data"):
            try:
                return json.loads(message["extracted_data"])
            except:
                pass
    return None

def create_dynamic_system_prompt_phone(state: str, extracted_data: dict = None) -> str:
    """Create dynamic system prompt based on state."""
    base_prompts = f"{BASE_SYSTEM_PROMPTS}\n"
    
    if state == ConversationStatePhone.INITIAL:
        return f"{base_prompts}You're a personal phone bot assistant AI helping an individual set up a phone bot. Ask what they want to do: create message block, add functions, edit existing items, or list functions."
    elif state == ConversationStatePhone.COLLECTING_MESSAGE_BLOCK:
        return f"{base_prompts}{MESSAGE_BLOCK_PROMPT}"
    elif state in [ConversationStatePhone.COLLECTING_BOT_FUNCTIONS, ConversationStatePhone.WAITING_FUNCTION_CONFIRMATION]:
        function_count = len(extracted_data.get("bot_functions", [])) if extracted_data else 0
        return f"{base_prompts}{BOT_FUNCTION_PROMPT.format(function_count=function_count)}"
    elif state in [ConversationStatePhone.MESSAGE_BLOCK_POSTED, ConversationStatePhone.BOT_FUNCTION_POSTED]:
        return f"{base_prompts}Ask if they want to add another function or if they're done."
    elif state == ConversationStatePhone.LISTING_INTERPRETATIONS:
        return f"{base_prompts}Show the current functions and ask what they want to do next: edit, add new, or create message block."
    elif state == ConversationStatePhone.SELECTING_EDIT_TYPE:
        return f"{base_prompts}Ask whether they want to edit the situation block or prompt block."
    elif state == ConversationStatePhone.SELECTING_INTERPRETATION:
        return f"{base_prompts}Help them select which interpretation to edit from the list."
    elif state == ConversationStatePhone.COLLECTING_SITUATION_UPDATE:
        return f"{base_prompts}Collect the new situation description for the selected interpretation."
    elif state == ConversationStatePhone.COLLECTING_PROMPT_UPDATE:
        return f"{base_prompts}Collect new prompt message and logic for the selected interpretation."
    elif state == ConversationStatePhone.COMPLETED:
        return f"{base_prompts}The setup is complete. Ask what they want to do next."
    return f"{base_prompts}Continue the conversation naturally."

def generate_interpretations_list_message_phone(bot_functions: List[Dict]) -> str:
    """Generate formatted list of bot functions and interpretations."""
    if not bot_functions:
        return "No functions found. Would you like to create a new function or message block?"
    
    message_parts = ["Here are your current functions:\n"]
    for func in bot_functions:
        function_name = func.get("name", "Unnamed")
        message_parts.append(f"**{function_name}:**")
        interpretations = func.get("interpretations", [])
        if not interpretations:
            message_parts.append("  • No interpretations")
        else:
            for interp in interpretations:
                message_parts.append(f"  • {interp.get('name', 'Unnamed')}")
        message_parts.append("")
    
    message_parts.append("What would you like to do?\n- Edit an existing function\n- Add a new function\n- Create a message block")
    return "\n".join(message_parts)

async def generate_welcome_message_phone(user_id: str, conversations: dict) -> str:
    """Generate a welcome message."""
    try:
        response = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": WELCOME_GENERATION_PROMPT},
                    {"role": "user", "content": "Generate welcome message for phone bot creation."}
                ],
                max_tokens=60,
                temperature=0.7
            )
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        logger.error(f"Error generating welcome message: {str(e)}")
        return "Hi! I'm your Phone Assistant. What would you like to do? Create a message block, add functions, edit existing ones, or list current functions?"

async def detect_intent_phone(conversation: list, current_state: str = None) -> str:
    """Enhanced intent detection with state awareness."""
    if not conversation:
        return "continue"
    
    message = conversation[-1].get("content", "").lower().strip()
    
    # Quick keyword-based detection first
    if any(k in message for k in ["list", "show", "view", "current", "display"]) and not any(k in message for k in ["edit", "update"]):
        return "list"
    if any(k in message for k in ["edit", "modify", "update", "change"]):
        return "update"
    if any(k in message for k in ["situation", "trigger"]) and current_state in [ConversationStatePhone.SELECTING_EDIT_TYPE, ConversationStatePhone.SELECTING_INTERPRETATION]:
        return "situation"
    if any(k in message for k in ["prompt", "message", "logic"]) and current_state in [ConversationStatePhone.SELECTING_EDIT_TYPE, ConversationStatePhone.SELECTING_INTERPRETATION]:
        return "prompt"
    if any(k in message for k in ["yes", "confirm", "proceed", "ok", "okay"]):
        return "create"
    if any(k in message for k in ["done", "finished", "no more", "complete"]):
        return "done"
    if any(k in message for k in ["create", "add", "new", "make"]):
        return "create"
    
    # Fallback to AI-based detection for complex cases
    try:
        recent_messages = conversation[-2:] if len(conversation) >= 2 else conversation
        context = "\n".join(f"{msg.get('role', '')}: {msg.get('content', '')}" for msg in recent_messages)
        context += f"\nCurrent state: {current_state}"
        
        response = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": f"{INTENT_DETECTION_PROMPT}\nCurrent conversation state: {current_state}"},
                    {"role": "user", "content": context}
                ],
                max_tokens=10,
                temperature=0.1
            )
        )
        detected_intent = response.choices[0].message.content.strip().lower()
        logger.info(f"AI detected intent: {detected_intent} for message: {message}")
        return detected_intent
    except Exception as e:
        logger.error(f"Error detecting intent: {str(e)}")
        return "continue"

def load_conversations_phone() -> dict:
    """Load conversations from JSON file."""
    try:
        if os.path.exists(CONVERSATIONS_FILE):
            with open(CONVERSATIONS_FILE, "r") as f:
                return json.load(f)
        return {}
    except Exception as e:
        logger.error(f"Error loading conversations: {str(e)}")
        return {}

def save_conversations_phone(conversations: dict):
    """Save conversations to JSON file."""
    try:
        with open(CONVERSATIONS_FILE, "w") as f:
            json.dump(conversations, f, indent=2)
    except Exception as e:
        logger.error(f"Error saving conversations: {str(e)}")

async def extract_message_block_data_phone(conversation: list) -> Optional[dict]:
    """Extract message block data (optimized)."""
    try:
        input_text = "\n".join(f"{msg.get('role', '').capitalize()}: {msg.get('content', '')}" for msg in conversation[-5:])
        response = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": DATA_EXTRACTION_PROMPT},
                    {"role": "user", "content": f"{input_text}\n\nExtract only MESSAGE_BLOCK data (message and logic). Return JSON or 'None'."}
                ],
                max_tokens=150,
                temperature=0.3
            )
        )
        result = response.choices[0].message.content.strip()
        if result == "None":
            return None
        parsed = json.loads(result)
        if "message_block" in parsed and parsed["message_block"].get("message") and parsed["message_block"].get("logic"):
            return parsed["message_block"]
        return None
    except Exception as e:
        logger.error(f"Error extracting message block: {str(e)}")
        return None

async def extract_single_bot_function_data_phone(conversation: list) -> Optional[dict]:
    """Extract single bot function data (optimized)."""
    try:
        input_text = "\n".join(f"{msg.get('role', '').capitalize()}: {msg.get('content', '')}" for msg in conversation[-7:])
        response = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": SINGLE_FUNCTION_EXTRACTION_PROMPT},
                    {"role": "user", "content": input_text}
                ],
                max_tokens=250,
                temperature=0.3
            )
        )
        result = response.choices[0].message.content.strip()
        if result == "null":
            return None
        return json.loads(result)
    except Exception as e:
        logger.error(f"Error extracting single bot function: {str(e)}")
        return None

async def check_message_block_complete_phone(conversation: list) -> bool:
    """Check if message block is complete (optimized)."""
    try:
        input_text = "\n".join(f"{msg.get('role', '')}: {msg.get('content', '')}" for msg in conversation[-5:])
        response = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "Check if user provided welcome message and behavioral logic. avoid these 'Hello and welcome to [Your Company/Service Name]!'. Return 'complete' or 'incomplete'. "},
                    {"role": "user", "content": input_text}
                ],
                max_tokens=10,
                temperature=0.1
            )
        )
        return response.choices[0].message.content.strip().lower() == "complete"
    except Exception as e:
        logger.error(f"Error checking message block completion: {str(e)}")
        return False

async def check_single_bot_function_complete_phone(conversation: list) -> bool:
    """Check if single bot function is complete (optimized)."""
    try:
        input_text = "\n".join(f"{msg.get('role', '')}: {msg.get('content', '')}" for msg in conversation[-7:])
        response = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "Check if user provided complete bot function (name, sub-function, situation, prompt, action). Return 'complete' or 'incomplete'."},
                    {"role": "user", "content": input_text}
                ],
                max_tokens=10,
                temperature=0.1
            )
        )
        return response.choices[0].message.content.strip().lower() == "complete"
    except Exception as e:
        logger.error(f"Error checking bot function completion: {str(e)}")
        return False

def function_exists_phone(bot_functions: List[Dict], function_name: str, sub_function_name: Optional[str] = None) -> bool:
    """Check if a bot function or interpretation exists."""
    fn_lower = function_name.lower()
    for func in bot_functions:
        if func.get("name", "").lower() == fn_lower:
            if sub_function_name and any(interp.get("name", "").lower() == sub_function_name.lower() for interp in func.get("interpretations", [])):
                return True
            return True
    return False

# Background task functions
async def post_conversation_block_task_phone(message_block: dict, bearer_token: str) -> bool:
    """Post conversation block as background task."""
    url = f"{BASE_URL}/knowledge-base/conversation-block"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
        "accept": "*/*"
    }
    payload = {
        "message": message_block.get("message", ""),
        "logic": message_block.get("logic", ""),
        "agentType": "Phone"
    }
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(url, headers=headers, json=payload) as response:
                response.raise_for_status()
                logger.info("Conversation block posted successfully")
                return True
        except aiohttp.ClientError as e:
            logger.error(f"Failed to create conversation block: {str(e)}")
            return False

async def post_single_bot_function_task_phone(bot_function: dict, bearer_token: str) -> bool:
    """Post single bot function as background task."""
    base_url = f"{BASE_URL}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
        "accept": "*/*"
    }
    async with aiohttp.ClientSession() as session:
        try:
            function_name = bot_function.get("bot_function", "")
            function_value = function_name.replace(" ", "_").lower()
            
            # Step 1: Create bot function
            function_payload = {"name": function_name, "value": function_value}
            async with session.post(f"{base_url}/phone-bot-functions", headers=headers, json=function_payload) as response:
                response.raise_for_status()
                function_data = await response.json()
            
            # Step 2: Create interpretation
            sub_function_name = bot_function.get("sub_bot_function", f"General {function_name}")
            sub_function_value = sub_function_name.replace(" ", "_").lower()
            interpretation_payload = {
                "botFunctionsValue": function_value,
                "name": sub_function_name,
                "value": sub_function_value
            }
            async with session.post(f"{base_url}/phone-bot-functions/interpretation", headers=headers, json=interpretation_payload) as response:
                response.raise_for_status()
                interpretation_data = await response.json()
                interpretation_id = interpretation_data.get("result", {}).get("_id")
            
            if not interpretation_id:
                logger.error(f"No interpretation ID for {function_name}")
                return False
            
            # Step 3: Get bot function detailed
            async with session.get(f"{base_url}/phone-bot-function-detailed?interpretationId={interpretation_id}", headers=headers) as response:
                response.raise_for_status()
                detailed_data = await response.json()
                bot_function_detailed_id = detailed_data.get("result", {}).get("_id")
            
            if not bot_function_detailed_id:
                logger.error(f"No detailed function ID for {function_name}")
                return False
            
            # Step 4: Create situation
            cognitive_model = bot_function.get("cognitive_model", {})
            situation_name = cognitive_model.get("situation_block", "")
            if situation_name:
                situation_payload = {
                    "botFunctionDetailedId": bot_function_detailed_id,
                    "name": situation_name
                }
                async with session.post(f"{base_url}/phone-bot-function-detailed/situation", headers=headers, json=situation_payload) as response:
                    response.raise_for_status()
            
            # Step 5: Create prompt block
            prompt_message = cognitive_model.get("prompt_block", "")
            prompt_logic = cognitive_model.get("action_block", "")
            if prompt_message or prompt_logic:
                prompt_payload = {
                    "botFunctionDetailedId": bot_function_detailed_id,
                    "message": prompt_message,
                    "logic": prompt_logic
                }
                async with session.put(f"{base_url}/phone-bot-function-detailed/prompt-block", headers=headers, json=prompt_payload) as response:
                    response.raise_for_status()
            
            logger.info(f"Bot function {function_name} posted successfully")
            return True
        except aiohttp.ClientError as e:
            logger.error(f"API request failed: {str(e)}")
            return False

async def update_situation_task_phone(bot_function_detailed_id: str, situation_id: str, new_name: str, bearer_token: str) -> bool:
    """Update situation as background task."""
    try:
        success = await update_situation_block_phone(bot_function_detailed_id, situation_id, new_name, bearer_token)
        logger.info(f"Situation update task completed: {success}")
        return success
    except Exception as e:
        logger.error(f"Error in situation update task: {str(e)}")
        return False

async def update_prompt_task_phone(bot_function_detailed_id: str, new_message: str, new_logic: str, bearer_token: str) -> bool:
    """Update prompt as background task."""
    try:
        success = await update_prompt_block_phone(bot_function_detailed_id, new_message, new_logic, bearer_token)
        if success:
            logger.info(f"Prompt update task completed successfully")
        else:
            logger.error(f"Prompt update task failed")
        return success
    except Exception as e:
        logger.error(f"Error in prompt update task: {str(e)}")
        return False

async def generate_completion_message_phone() -> str:
    """Generate completion message (optimized)."""
    try:
        response = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": COMPLETION_PROMPT},
                    {"role": "user", "content": "Generate completion message."}
                ],
                max_tokens=50,
                temperature=0.7
            )
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        logger.error(f"Error generating completion message: {str(e)}")
        return "Great! Your bot setup is complete. Would you like to add more functions, edit existing ones, or list all functions?"

async def generate_error_message_phone(error_context: str = "") -> str:
    """Generate error message (optimized)."""
    try:
        response = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": ERROR_HANDLING_PROMPT},
                    {"role": "user", "content": f"Generate error message for: {error_context}"}
                ],
                max_tokens=50,
                temperature=0.7
            )
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        logger.error(f"Error generating error message: {str(e)}")
        return "Something went wrong. Let's try again or continue with something else."

def parse_prompt_update_phone(message: str) -> tuple:
    """Parse message and logic from user input."""
    message_clean = message.strip()
    
    # Try format with explicit labels
    if "message:" in message_clean.lower() and "logic:" in message_clean.lower():
        parts = re.split(r'logic:', message_clean, flags=re.IGNORECASE)
        if len(parts) >= 2:
            msg_part = re.sub(r'message:', '', parts[0], flags=re.IGNORECASE).strip()
            logic_part = parts[1].strip()
            return msg_part, logic_part
    
    # Try two-line format
    lines = [line.strip() for line in message_clean.split('\n') if line.strip()]
    if len(lines) >= 2:
        return lines[0], lines[1]
    
    return "", ""