import os
import requests
from fastapi import  <PERSON>TTP<PERSON>xception
from PIL import Image, ImageDraw, ImageFont
import re
import openai
from dotenv import load_dotenv
import json
import os
import re
import requests
import logging
from fastapi import HTTPException
from openai import OpenAI
from PIL import Image
from pathlib import Path
import io
from fastapi import UploadFile
from typing import Optional


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

CONVERSATION_FILE = "conversation_history_social_media_bot.json"


OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
X_API_KEY = os.getenv("X_API_KEY")
BASE_URL = os.getenv("BASE_URL")

# Validate environment variables
if not OPENAI_API_KEY:
    logger.error("OPENAI_API_KEY is not set in .env file")
    raise ValueError("OPENAI_API_KEY is not set in .env file")
if not X_API_KEY:
    logger.error("X_API_KEY is not set in .env file")
    raise ValueError("X_API_KEY is not set in .env file")
if not BASE_URL:
    logger.error("BASE_URL is not set in .env file")
    raise ValueError("BASE_URL is not set in .env file")

# Initialize OpenAI client
client = OpenAI(api_key=OPENAI_API_KEY)

STATIC_DIR = Path.cwd() / "static"
STATIC_DIR.mkdir(exist_ok=True)

def load_prompts():
    with open("social_media_bot_prompts.json", "r") as f:
        return json.load(f)

PROMPTS = load_prompts()
CHATBOT_PROMPT = PROMPTS["chatbot_prompt"]
EXTRACT_PROMPT = PROMPTS["extract_prompt"]


openai.api_key = os.getenv('OPENAI_API_KEY')
BASE_URL = os.getenv("BASE_URL")
BEARER_TOKEN = os.getenv("BEARER_TOKEN")


def add_fade_to_background(image, fade_color=(255, 255, 255)):
    """ Fades the image from opaque at the top to a specific background color at the bottom. """
    width, height = image.size
    fade_layer = Image.new("RGBA", (width, height), fade_color)

    gradient = Image.new("L", (1, height))
    for y in range(height):
        alpha = int(255 * (1 - y / height))  
        gradient.putpixel((0, y), alpha)
    alpha_gradient = gradient.resize(image.size)

    image_with_alpha = image.convert("RGBA")
    faded_image = Image.composite(image_with_alpha, fade_layer, alpha_gradient)
    return faded_image

def add_border(image, border_size, color):
    width, height = image.size
    new_width = width + 2 * border_size
    new_height = height + 2 * border_size
    bordered_image = Image.new("RGB", (new_width, new_height), color)
    bordered_image.paste(image, (border_size, border_size))
    return bordered_image

def add_fade_to_background(image, fade_color=(255, 255, 255)):
    width, height = image.size
    fade_layer = Image.new("RGBA", (width, height), fade_color)

    gradient = Image.new("L", (1, height))
    for y in range(height):
        alpha = int(255 * (1 - y / height))  
        gradient.putpixel((0, y), alpha)
    alpha_gradient = gradient.resize(image.size)

    image_with_alpha = image.convert("RGBA")
    faded_image = Image.composite(image_with_alpha, fade_layer, alpha_gradient)
    return faded_image

def hex_to_rgb(hex_color):
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

def draw_multiline_text(draw, content, position, font, max_width, max_lines=3, fill="white"):
    """Draws multiline text, wrapping the words to fit within max_width and limiting to max_lines."""
    if content is None:
        return

    words = content.split(" ")
    lines = []
    current_line = []
    current_width = 0

    for word in words:
        word_width = draw.textbbox((0, 0), word, font=font)[2]
        space_width = draw.textbbox((0, 0), " ", font=font)[2]
        if current_width + word_width <= max_width:
            current_line.append(word)
            current_width += word_width + space_width
        else:
            lines.append(" ".join(current_line))
            current_line = [word]
            current_width = word_width
        if len(lines) >= max_lines:
            break

    if current_line and len(lines) < max_lines:
        lines.append(" ".join(current_line))

    y_offset = position[1]
    for line in lines:
        draw.text((position[0], y_offset), line, font=font, fill=fill)
        line_height = draw.textbbox((0, 0), line, font=font)[3] - draw.textbbox((0, 0), line, font=font)[1]
        y_offset += line_height + 10

def fit_text_to_box(draw, text, position, max_width, max_height, font_path, initial_font_size, max_lines, fill="white"):
    """Resize text to fit within the max_width and max_height."""
    font = ImageFont.truetype(font_path, initial_font_size)
    lines = []
    while True:
        current_line = []
        current_width = 0
        words = text.split()
        
        for word in words:
            word_width = draw.textbbox((0, 0), word, font=font)[2]
            space_width = draw.textbbox((0, 0), " ", font=font)[2]
            if current_width + word_width <= max_width:
                current_line.append(word)
                current_width += word_width + space_width
            else:
                lines.append(" ".join(current_line))
                current_line = [word]
                current_width = word_width
            if len(lines) >= max_lines:
                break

        if len(lines) <= max_lines:
            break
        
        initial_font_size -= 2
        font = ImageFont.truetype(font_path, initial_font_size)

    draw_multiline_text(draw, text, position, font, max_width, max_lines, fill)

def resize_image_with_aspect_ratio(image, max_width, max_height):
    """ Resize the image while maintaining aspect ratio """
    original_width, original_height = image.size
    aspect_ratio = original_width / original_height

    if original_width > original_height:
        new_width = min(max_width, original_width)
        new_height = int(new_width / aspect_ratio)
    else:
        new_height = min(max_height, original_height)
        new_width = int(new_height * aspect_ratio)

    resized_image = image.resize((new_width, new_height), Image.LANCZOS)

    new_image = Image.new(image.mode, (max_width, max_height), (255, 255, 255, 0) if image.mode == "RGBA" else "white")
    
    paste_position = (
        (max_width - new_width) // 2,
        (max_height - new_height) // 2
    )
    new_image.paste(resized_image, paste_position, resized_image if image.mode == "RGBA" else None)

    return new_image

def create_poster(
    main_image: Image.Image,
    sub_image: Image.Image,
    main_text: str,
    content: str,
    location:str,  
    price_text: str,  
    contact_info: str,  
    website: str,  
    features: str,  
    logo_image: Image.Image,
    output_path: str,
    color1: str,
    color2: str
):
    color1_rgb = hex_to_rgb(color1)
    color2_rgb = hex_to_rgb(color2)

    if logo_image.mode != 'RGBA':
        logo_image = logo_image.convert('RGBA')

    canvas_width, canvas_height = 1080, 1080
    template_image = Image.new("RGB", (canvas_width, canvas_height), color="white") 
    draw = ImageDraw.Draw(template_image)
    boxes = [
        {"coords": [(405, 55), (1025, 490)], "color": color1_rgb},
        {"coords": [(60, 486), (630, 920)], "color": color1_rgb},
        {"coords": [(120, 171), (955, 600)], "color": color2_rgb},
        {"coords": [(670, 735), (1025, 790)], "color": color1_rgb},
        {"coords": [(60, 940), (1025, 1015)], "color": color2_rgb}
    ]

    for box in boxes:
        draw.rectangle(box["coords"], fill=box["color"])

    font_path = "Roboto-Bold.ttf" 
    font_large = ImageFont.truetype(font_path, 48)
    font_small = ImageFont.truetype(font_path, 24)
    font_medium = ImageFont.truetype(font_path, 35)
    font_medium1 = ImageFont.truetype(font_path, 40)

    max_main_text_width = 500  
    if main_text:
        fit_text_to_box(draw, main_text, (120, 620), max_main_text_width, max_height=140, font_path=font_path, initial_font_size=48, max_lines=2, fill="white")

    if content:
        fit_text_to_box(draw, content, (120, 740), max_width=450, max_height=80, initial_font_size=24, font_path=font_path, max_lines=3, fill="white")

    if location:
        draw.text((120, 850), location, font=font_small, fill="white")

    if price_text:
        draw.text((430, 90), f'STARTING PRICE - {price_text}', font=font_medium1, fill="white")

    draw.text((120, 960), "Contact Us Today!", font=font_small, fill="white")
    contact_text = ""
    if contact_info and website:
        contact_text = f"{contact_info} | {website}"
    elif contact_info:
        contact_text = contact_info
    elif website:
        contact_text = website  

    if contact_text:

        contact_text_width = draw.textbbox((0, 0), contact_text, font=font_small)[2]
        right_aligned_x = canvas_width - contact_text_width - 100

        draw.text((right_aligned_x, 960), contact_text, font=font_small, fill="white")

    if features:
        draw.text((695, 740), "Features", font=font_medium, fill="white")
        feature_list = features.split(',')
        bullet = "\u2022"
        feature_start_y = 800
        for feature in feature_list:
            feature = feature.strip()
            draw.text((695, feature_start_y), f"{bullet} {feature}", font=font_small, fill=color1_rgb)
            feature_start_y += 40

    main_image = main_image.resize((850, 430))
    sub_image = sub_image.resize((330, 162))
    sub_image = add_border(sub_image, 10, color1_rgb)
 
    template_image.paste(main_image, (120, 170))
    template_image.paste(sub_image, (670, 533))

    logo_image = resize_image_with_aspect_ratio(logo_image, 284, 113)
    template_image.paste(logo_image, (120, 55), logo_image)

    template_image.save(output_path)


def get_user_details(user_id):

    url = f"{BASE_URL}/api/botsworkai/users/customerProfile/{user_id}"

    headers = {
        "Authorization": f"Bearer {BEARER_TOKEN}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        user_data = response.json()
        return user_data
    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        return None



def generate_caption(main_heading: str, content: str = None, features: str = None) -> dict:
    messages = [
        {"role": "system", "content": "You are an expert at writing catchy and engaging captions for promotional materials."},
        {"role": "user", "content": f"Create a catchy and engaging caption. The main heading is '{main_heading}'."}
    ]
    
    if content:
        messages.append({"role": "user", "content": f"The content or description is: '{content}'."})
    
    if features:
        messages.append({"role": "user", "content": f"Key features include: {features}."})
    
    messages.append({"role": "user", "content": "The caption should attract attention, engage the audience, and spark curiosity. Include 3 unique hashtags in the caption."})

    try:
        response = openai.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=messages,
            max_tokens=100,
            temperature=0.7
        )

        response_content = response.choices[0].message.content

        caption = response_content.split("#")[0].strip()
        hashtags = re.findall(r"#\w+", response_content)


        return {
            "caption": caption,
            "hashtags": hashtags
        }

    except openai.OpenAIError as e:
        print(f"OpenAI API error: {e}")
        raise ValueError("Failed to generate caption. Please try again later.")
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise ValueError("An unexpected error occurred during caption generation.")
    

# Load conversation history from JSON file
def load_conversation_history(user_id: str) -> dict:
    if os.path.exists(CONVERSATION_FILE):
        with open(CONVERSATION_FILE, "r") as f:
            data = json.load(f)
            return data.get(user_id, {"user_id": user_id, "status": "pending", "conversation": [], "image_count": 0})
    return {"user_id": user_id, "status": "pending", "conversation": [], "image_count": 0}

# Save conversation history to JSON file
def save_conversation_history(user_id: str, history: dict):
    all_data = {}
    if os.path.exists(CONVERSATION_FILE):
        with open(CONVERSATION_FILE, "r") as f:
            all_data = json.load(f)
    all_data[user_id] = history
    with open(CONVERSATION_FILE, "w") as f:
        json.dump(all_data, f, indent=2)

async def extract_and_print_details(user_id: str, conversation: list):
    messages = [
        {"role": "system", "content": EXTRACT_PROMPT},
        {"role": "user", "content": json.dumps(conversation, indent=2)}
    ]
    max_retries = 10
    retry_count = 0

    while retry_count < max_retries:
        try:
            completion = client.chat.completions.create(
                model="gpt-4o",
                messages=messages,
                max_tokens=200
            )
            response_content = completion.choices[0].message.content.strip()
            logger.info(f"Raw extraction response (attempt {retry_count + 1}): {response_content}")
            try:
                extracted_details = json.loads(response_content)
                required_keys = {"main_text", "sub_text", "features", "location"}
                if not all(key in extracted_details for key in required_keys):
                    logger.warning(f"Missing required keys in extracted details: {extracted_details}")
                    raise json.JSONDecodeError("Missing required keys", response_content, 0)
                print("Extracted Poster Details:")
                print(f"Main Text: {extracted_details['main_text']}")
                print(f"Sub Text: {extracted_details['sub_text']}")
                print(f"Features: {extracted_details['features']}")
                print(f"Location: {extracted_details['location']}")
                return extracted_details
            except json.JSONDecodeError as e:
                logger.error(f"JSON parsing error on attempt {retry_count + 1}: {str(e)}")
                retry_count += 1
                if retry_count == max_retries:
                    logger.error(f"Failed to parse JSON after {max_retries} attempts")
                    return {
                        "main_text": "",
                        "sub_text": "",
                        "features": "",
                        "location": ""
                    }
        except Exception as e:
            logger.error(f"Error extracting details on attempt {retry_count + 1}: {str(e)}")
            retry_count += 1
            if retry_count == max_retries:
                logger.error(f"Failed to extract details after {max_retries} attempts")
                return {
                    "main_text": "",
                    "sub_text": "",
                    "features": "",
                    "location": ""
                }

# Delete conversation history for a user
def delete_conversation_history(user_id: str):
    all_data = {}
    if os.path.exists(CONVERSATION_FILE):
        with open(CONVERSATION_FILE, "r") as f:
            all_data = json.load(f)
        all_data.pop(user_id, None)
        with open(CONVERSATION_FILE, "w") as f:
            json.dump(all_data, f, indent=2)

# Check if the user's message is a confirmation
def is_confirmation_message(message: str) -> bool:
    confirmation_phrases = [
        r"\b(yes|correct|that's right|confirm|okay|ok|yeah|yep)\b",
        r"\b(looks good|fine|all good|good to go)\b"
    ]
    message = message.lower().strip()
    return any(re.search(phrase, message, re.IGNORECASE) for phrase in confirmation_phrases)

# Check if the bot's response is a confirmation message
def is_confirmation_response(response: str) -> bool:
    exact_confirmation = "Thank you! I have all the details needed for your poster. Please confirm if everything looks correct or let me know if you need any changes."
    confirmation_keywords = [
        r"thank you.*all the details.*poster.*confirm.*correct.*changes",
        r"have all the details.*poster.*confirm",
        r"poster.*details.*thank you.*confirm"
    ]
    response_lower = response.lower().strip()
    return response == exact_confirmation or any(
        re.search(pattern, response_lower, re.IGNORECASE) for pattern in confirmation_keywords
    )

# Function to fetch user details
def get_user_details(user_id: str, bearer_token: str) -> dict:
    url = f"{BASE_URL}/users/customerProfile/{user_id}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "accept": "*/*"
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        user_data = response.json()
        logger.info(f"Successfully fetched user details for user_id {user_id}")
        
        result = user_data.get('result')
        if result is None:
            logger.error(f"No user details found for user_id {user_id}")
            raise HTTPException(status_code=404, detail="Branding user details not found")
        
        profile = result.get('profile', {})
        website = profile.get('website', '')
        contact_info = profile.get('phoneNumber', '')
        
        return {"website": website, "contact_info": contact_info}
    
    except requests.RequestException as e:
        logger.error(f"Error fetching user details for user_id {user_id}: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response content: {e.response.text}")
        raise HTTPException(status_code=500, detail=f"Error fetching user details: {str(e)}")

# Function to prepare the poster by fetching a bearer token
def preparing_poster(user_id: str) -> tuple:
    url = f"{BASE_URL}/auth/user/token?userId={user_id}"
    headers = {
        "accept": "*/*",
        "X-API-Key": X_API_KEY
    }
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        data = response.json()
        if data.get("status") == "SUCCESS" and "result" in data:
            token = data["result"]
            logger.info(f"Successfully fetched bearer token for user_id {user_id}")
            user_details = get_user_details(user_id, token)
            return token, user_details
        else:
            logger.error(f"Unexpected response format or status from token API for user_id {user_id}: {data}")
            return None, None
    except requests.RequestException as e:
        logger.error(f"Error fetching bearer token for user_id {user_id}: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response content: {e.response.text}")
        return None, None

async def social_media_chat_endpoint(
    user_id: str,
    message: str,
    image1: Optional[UploadFile] = None,
    image2: Optional[UploadFile] = None
):
    # Load conversation history
    history = load_conversation_history(user_id)
    conversation = history["conversation"]
    status = history.get("status", "pending")
    image_count = history.get("image_count", 0)

    # If status is "completed", handle image uploads and poster creation
    if status == "completed":
        valid_images = [img for img in [image1, image2] if img is not None]
        images_provided = len(valid_images) > 0

        if images_provided:
            image_count += len(valid_images)
            history["image_count"] = image_count
            conversation.append({"sender": "user", "message": f"Uploaded {len(valid_images)} image(s)"})

            if image_count >= 2:
                # Extract poster details
                extracted_details = await extract_and_print_details(user_id, conversation)
                
                # Validate extracted details
                required_fields = ["main_text", "sub_text", "features", "location"]
                if not all(extracted_details.get(field) for field in required_fields):
                    response = "Incomplete poster details. Please provide all required details and try again."
                    conversation.append({"sender": "bot", "message": response})
                    save_conversation_history(user_id, history)
                    return {"response": response, "user_id": user_id}

                # Fetch bearer token and user details
                token, user_details = preparing_poster(user_id)
                if not token or not user_details:
                    response = "Failed to prepare the poster due to an issue with token retrieval or user details. Please try again later."
                    conversation.append({"sender": "bot", "message": response})
                    save_conversation_history(user_id, history)
                    return {"response": response, "user_id": user_id}

                # Process uploaded images
                try:
                    main_image = Image.open(io.BytesIO(await valid_images[0].read()))
                    sub_image = Image.open(io.BytesIO(await valid_images[1].read())) if len(valid_images) > 1 else None
                    if not sub_image:
                        response = "Please upload a second image to complete the poster."
                        conversation.append({"sender": "bot", "message": response})
                        save_conversation_history(user_id, history)
                        return {"response": response, "user_id": user_id}
                except Exception as e:
                    logger.error(f"Error processing images: {str(e)}")
                    response = "Error processing uploaded images. Please ensure valid image files are provided."
                    conversation.append({"sender": "bot", "message": response})
                    save_conversation_history(user_id, history)
                    return {"response": response, "user_id": user_id}

                # Prepare parameters for create_poster
                output_path = f"{user_id}_output_poster1.jpg"
                poster_path = STATIC_DIR / output_path
                logo_path = Path.cwd() / "logo.png"
                try:
                    logo_image = Image.open(logo_path)
                except FileNotFoundError:
                    logger.error(f"Logo file not found at {logo_path}")
                    response = "Error: Logo file not found. Please contact support."
                    conversation.append({"sender": "bot", "message": response})
                    save_conversation_history(user_id, history)
                    return {"response": response, "user_id": user_id}

                # Call create_poster
                try:
                    create_poster(
                        main_image=main_image,
                        sub_image=sub_image,
                        main_text=extracted_details["main_text"],
                        output_path=str(poster_path),
                        content=extracted_details["sub_text"],
                        location=extracted_details["location"],
                        website=user_details["website"],
                        price_text="",  # Not collected; adjust if needed
                        contact_info=user_details["contact_info"],
                        features=extracted_details["features"],
                        logo_image=logo_image,
                        color1="#23969d",
                        color2="#145a5f"
                    )
                except Exception as e:
                    logger.error(f"Error creating poster: {str(e)}")
                    response = f"Error creating poster: {str(e)}"
                    conversation.append({"sender": "bot", "message": response})
                    save_conversation_history(user_id, history)
                    return {"response": response, "user_id": user_id}

                # Upload the poster
                upload_url = f"{BASE_URL}/file/upload/IMAGE"
                try:
                    with open(poster_path, "rb") as file:
                        files = {"file": (output_path, file, "image/jpeg")}
                        data = {
                            "fileCategory": "IMAGE",
                            "fileName": output_path
                        }
                        headers = {
                            "Authorization": f"Bearer {token}",
                            "accept": "*/*"
                        }
                        upload_response = requests.post(upload_url, files=files, data=data, headers=headers)
                        upload_response.raise_for_status()
                        upload_result = upload_response.json()
                        if upload_result.get("status") != "SUCCESS":
                            raise HTTPException(status_code=500, detail="File upload failed")
                        file_name = upload_result["result"].get("fileName")
                        if not file_name:
                            raise HTTPException(status_code=500, detail="File name missing in upload response")
                    logger.info(f"Successfully uploaded poster: {file_name}")
                except requests.RequestException as e:
                    logger.error(f"Error uploading poster: {str(e)}")
                    response = f"Error uploading poster: {str(e)}"
                    conversation.append({" Trailer": "bot", "message": response})
                    save_conversation_history(user_id, history)
                    return {"response": response, "user_id": user_id}
                except Exception as e:
                    logger.error(f"Error accessing poster file: {str(e)}")
                    response = f"Error accessing poster file: {str(e)}"
                    conversation.append({"sender": "bot", "message": response})
                    save_conversation_history(user_id, history)
                    return {"response": response, "user_id": user_id}

                # Retrieve signed URL
                get_file_url = f"{BASE_URL}/file/IMAGE/{file_name}"
                try:
                    get_headers = {
                        "Authorization": f"Bearer {token}",
                        "accept": "*/*"
                    }
                    get_response = requests.get(get_file_url, headers=get_headers)
                    get_response.raise_for_status()
                    get_result = get_response.json()
                    if get_result.get("status") == "SUCCESS" and "result" in get_result:
                        signed_url = get_result["result"].get("signedUrl")
                        if not signed_url:
                            raise HTTPException(status_code=500, detail="Signed URL missing in response")
                    else:
                        raise HTTPException(status_code=500, detail="Failed to retrieve signed URL")
                    logger.info(f"Successfully retrieved signed URL: {signed_url}")
                except requests.RequestException as e:
                    logger.error(f"Error retrieving signed URL: {str(e)}")
                    response = f"Error retrieving signed URL: {str(e)}"
                    conversation.append({"sender": "bot", "message": response})
                    save_conversation_history(user_id, history)
                    return {"response": response, "user_id": user_id}

                # Generate caption and hashtags
                try:
                    caption_data = generate_caption(
                        main_heading=extracted_details["main_text"],
                        content=extracted_details["sub_text"],
                        features=extracted_details["features"]
                    )
                    clean_caption = caption_data['caption'].replace('"', '').replace("\\", "").strip()
                    clean_hashtags = ", ".join(caption_data['hashtags'])
                    logger.info(f"Generated caption: {clean_caption}, Hashtags: {clean_hashtags}")
                except Exception as e:
                    logger.error(f"Error generating caption: {str(e)}")
                    response = f"Error generating caption: {str(e)}. Poster uploaded, but social media post not created."
                    conversation.append({"sender": "bot", "message": response})
                    save_conversation_history(user_id, history)
                    return {
                        "response": response,
                        "user_id": user_id,
                    }

                # Post to social media content API
                post_url = f"{BASE_URL}/social-media-content/add-content-user"
                headers = {
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
                request_body = {
                    "post": clean_caption,
                    "hashtags": clean_hashtags,
                    "image": signed_url,
                    "userId": user_id
                }
                try:
                    post_response = requests.post(post_url, json=request_body, headers=headers)
                    post_response.raise_for_status()
                    post_result = post_response.json()
                    if post_result.get("status") != "SUCCESS":
                        raise HTTPException(status_code=500, detail="Social media post failed")
                    logger.info(f"Successfully posted social media content for user_id {user_id}")
                except requests.RequestException as e:
                    logger.error(f"Error posting to social media API: {str(e)}")
                    response = f"Poster uploaded successfully, but failed to post to social media: {str(e)}. You can view the poster at: {signed_url}"
                    conversation.append({"sender": "bot", "message": response})
                    save_conversation_history(user_id, history)
                    return {
                        "response": response,
                        "user_id": user_id,
                    }

                # Clean up conversation history and temporary file
                delete_conversation_history(user_id)
                try:
                    if os.path.exists(poster_path):
                        os.remove(poster_path)
                        logger.info(f"Deleted temporary poster file: {poster_path}")
                except Exception as e:
                    logger.warning(f"Failed to delete temporary file {poster_path}: {str(e)}")

                # Return success response
                response = f"Poster created successfully. You can view it at: {signed_url}"
                return {
                    "response": response,
                    "user_id": user_id,
                }
            else:
                response = f"Please upload {2 - image_count} more image(s) to complete the poster."
                conversation.append({"sender": "bot", "message": response})
                save_conversation_history(user_id, history)
                return {"response": response, "user_id": user_id}
        else:
            response = f"Please upload {2 - image_count} image(s) to be included in the poster."
            conversation.append({"sender": "bot", "message": response})
            save_conversation_history(user_id, history)
            return {"response": response, "user_id": user_id}

    # Append user message to history
    conversation.append({"sender": "user", "message": message})

    # Prepare messages for OpenAI
    messages = [
        {"role": "system", "content": CHATBOT_PROMPT},
        *[{"role": "user" if entry["sender"] == "user" else "assistant", "content": entry["message"]}
          for entry in conversation]
    ]

    # Get response from OpenAI using GPT-4o
    try:
        completion = client.chat.completions.create(
            model="gpt-4o",
            messages=messages,
            max_tokens=200,
            temperature=0.3
        )
        response = completion.choices[0].message.content.strip()
    except Exception as e:
        logger.error(f"Error processing OpenAI request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")

    # Append bot response to history
    conversation.append({"sender": "bot", "message": response})

    # Check for confirmation
    if len(conversation) >= 4:
        last_assistant_message = next(
            (entry["message"] for entry in reversed(conversation[:-1]) if entry["sender"] == "bot"),
            None
        )
        if last_assistant_message and is_confirmation_response(last_assistant_message) and is_confirmation_message(message):
            history["status"] = "completed"
            response = "Great, thank you for confirming! Please upload two images to be included in the poster."
            conversation.append({"sender": "bot", "message": response})

    # Save updated history
    history["conversation"] = conversation
    history["image_count"] = image_count
    history["status"] = history.get("status", "pending")
    save_conversation_history(user_id, history)

    return {"response": response, "user_id": user_id}