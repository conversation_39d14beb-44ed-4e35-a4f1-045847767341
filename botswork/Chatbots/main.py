import asyncio
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks,Form, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from models import ChatMessage, ChatResponse,PostRequest, PostResponse
from dotenv import load_dotenv
import uvicorn
from typing import Optional
from email_chat_handler import handle_email_chat 
from receptionist_chat_handler import handle_receptionist_chat
from social_media_bot import social_media_chat_endpoint
from crm_chat_handler import handle_crm_chat
from social_media_post import generate_social_media_post_logic
from chatbot_phonebot import *
from research_bot import research_assistant
from research_fn import strip_markdown
import logging
import json
import os

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# app = FastAPI()
app = FastAPI(
    title="Botswork V2 API 🚀",
    description="This is a group of chatbots with different capabilities in V2.",
    version="1.0.2",
    terms_of_service="https://web.botswork.ai/terms-and-conditions",
    contact={
        "name": "Botswork Api Support",
        "url": "https://botswork.ai",
        "email": "<EMAIL>",
    },

)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
X_API_KEY = os.getenv("X_API_KEY")
BASE_URL = os.getenv("BASE_URL")


if not all([OPENAI_API_KEY, X_API_KEY, BASE_URL]):
    raise ValueError("Missing required environment variables")

@app.get("/", tags=["Test"], summary="Root Endpoint")
async def read_root():
    return {"message": "Welcome to the Botswork V2 Chatbot API!"}

@app.post("/social_media_post", response_model=PostResponse)
async def generate_social_media_post(request: PostRequest, background_tasks: BackgroundTasks):
    return await generate_social_media_post_logic(request, background_tasks)


@app.post("/social-media-chat", tags=["Chatbots"], summary="Socialmedia home chatbot")
# async def social_media_chat(user_id: str = Form(...), message: str = Form(...), image1: Optional[UploadFile] = File(None),image2: Optional[UploadFile] = File(None)):
async def social_media_chat(message: ChatMessage):
    image1 = None
    image2 = None
    return await social_media_chat_endpoint(message.user_id, message.message, image1, image2)

@app.post("/email-chat", tags=["Chatbots"], summary="Email Assistant Chat", response_model=ChatResponse)
async def email_chat(message: ChatMessage):
    try:
        if not message.user_id.strip() or not message.message.strip():
            logger.error(f"Invalid input: user_id={message.user_id}, message={message.message}")
            raise HTTPException(status_code=422, detail="user_id and message cannot be empty")
        
        response = await handle_email_chat(message)
        return response
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error processing email chat request: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.post("/crm-chat", tags=["Chatbots"], summary="CRM Assistant Chat", response_model=ChatResponse)
async def crm_chat(message: ChatMessage):
    try:
        if not message.user_id.strip() or not message.message.strip():
            logger.error(f"Invalid input: user_id={message.user_id}, message={message.message}")
            raise HTTPException(status_code=422, detail="user_id and message cannot be empty")
        
        response = await handle_crm_chat(message)
        return response
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
    
@app.post("/receptionist-chat", tags=["Chatbots"], summary="Receptionist Assistant Chat", response_model=ChatResponse)
async def receptionist_chat(message: ChatMessage):
    return await handle_receptionist_chat(message)


@app.post("/phone-chat", tags=["Chatbots"], summary="Phone home chatbot", response_model=ChatResponse)
async def chat_phonebot(message: ChatMessage, background_tasks: BackgroundTasks):
    try:
        bearer_token = await fetch_bearer_token_phone(message.user_id)
        if not bearer_token:
            raise HTTPException(status_code=401, detail="Failed to authenticate user")
        
        conversations = load_conversations_phone()
        current_state = get_conversation_state_phone(conversations, message.user_id)
        
        logger.info(f"User {message.user_id} - Current state: {current_state} - Message: {message.message}")
        
        # Handle new user or greeting
        if message.user_id not in conversations or is_greeting_message_phone(message.message):
            welcome_message = await generate_welcome_message_phone(message.user_id, conversations)
            system_prompt = create_dynamic_system_prompt_phone(ConversationStatePhone.INITIAL)
            
            conversations[message.user_id] = [
                {"role": "system", "content": system_prompt},
                {"role": "assistant", "content": welcome_message, "extracted_data": None, "state": ConversationStatePhone.INITIAL}
            ]
            save_conversations_phone(conversations)
            logger.info(f"New conversation started for user {message.user_id}")
            clean_welcome_message=strip_markdown(welcome_message)
            return ChatResponse(user_id=message.user_id, response=clean_welcome_message)
        
        conversations[message.user_id].append({"role": "user", "content": message.message})
        
        # Detect user intent with state awareness
        user_intent = await detect_intent_phone(conversations[message.user_id], current_state)
        logger.info(f"Detected intent: {user_intent} for state: {current_state}")
        
        # Handle list request - highest priority
        if user_intent == "list" or (is_list_interpretations_request_phone(message.message) and current_state == ConversationStatePhone.INITIAL):
            all_bot_functions = await fetch_all_bot_functions_phone(bearer_token)
            interpretations_message = generate_interpretations_list_message_phone(all_bot_functions)
            conversations[message.user_id].append({
                "role": "assistant",
                "content": interpretations_message,
                "extracted_data": json.dumps(all_bot_functions) if all_bot_functions else None,
                "state": ConversationStatePhone.LISTING_INTERPRETATIONS
            })
            save_conversations_phone(conversations)
            logger.info(f"Listed interpretations for user {message.user_id}")
            clean_interpretations_message=strip_markdown(interpretations_message)
            return ChatResponse(user_id=message.user_id, response=clean_interpretations_message)
        
        # Get all bot functions for edit operations
        all_bot_functions = await fetch_all_bot_functions_phone(bearer_token)
        
        # Handle edit workflow - Step 1: Initial edit request
        if ((current_state == ConversationStatePhone.INITIAL or current_state == ConversationStatePhone.LISTING_INTERPRETATIONS) 
            and (user_intent == "update" or is_edit_request_phone(message.message))):
            
            # Check if user mentioned specific interpretation name
            interpretation_to_edit = find_interpretation_by_name_phone(all_bot_functions, message.message.strip())
            
            if interpretation_to_edit:
                # Direct to edit type selection
                edit_question = f"What would you like to edit for **{interpretation_to_edit['interpretation']['name']}**?\n\n1. **Situation block** (trigger conditions)\n2. **Prompt block** (message and logic)\n\nPlease specify which one."
                conversations[message.user_id].append({
                    "role": "assistant",
                    "content": edit_question,
                    "extracted_data": json.dumps({"bot_functions": all_bot_functions, "target_interpretation": interpretation_to_edit}),
                    "state": ConversationStatePhone.SELECTING_EDIT_TYPE
                })
            else:
                # Show functions and ask what to edit
                interpretations_message = generate_interpretations_list_message_phone(all_bot_functions)
                edit_question = f"{interpretations_message}\n\nWhich function would you like to edit? Please specify the interpretation name."
                conversations[message.user_id].append({
                    "role": "assistant",
                    "content": edit_question,
                    "extracted_data": json.dumps(all_bot_functions),
                    "state": ConversationStatePhone.SELECTING_INTERPRETATION
                })
            
            save_conversations_phone(conversations)
            logger.info(f"Started edit workflow for user {message.user_id}")
            edit_questions=strip_markdown(edit_question)
            return ChatResponse(user_id=message.user_id, response=edit_questions)
        
        # Handle edit type selection - Step 2: Choose what to edit (situation vs prompt)
        if current_state == ConversationStatePhone.SELECTING_EDIT_TYPE:
            stored_data = json.loads(conversations[message.user_id][-2]["extracted_data"])
            
            if user_intent == "situation" or "situation" in message.message.lower():
                edit_type = "situation"
                target_interpretation = stored_data.get("target_interpretation") or stored_data.get("interpretation_to_edit")
                
                if target_interpretation:
                    # We already know which interpretation to edit
                    next_message = f"Editing situation for **{target_interpretation['interpretation']['name']}** in **{target_interpretation['function_name']}**.\n\nPlease provide the new situation description:"
                    next_state = ConversationStatePhone.COLLECTING_SITUATION_UPDATE
                    extracted_data = json.dumps({
                        "interpretation_to_edit": target_interpretation,
                        "edit_type": edit_type
                    })
                else:
                    # Need to select interpretation - fetch fresh bot functions
                    all_bot_functions = await fetch_all_bot_functions_phone(bearer_token)
                    next_message = "Which interpretation's situation would you like to edit? Please specify the interpretation name:"
                    next_state = ConversationStatePhone.SELECTING_INTERPRETATION
                    extracted_data = json.dumps({"bot_functions": all_bot_functions, "edit_type": edit_type})
                        
            elif user_intent == "prompt" or "prompt" in message.message.lower():
                edit_type = "prompt"
                target_interpretation = stored_data.get("target_interpretation") or stored_data.get("interpretation_to_edit")
                
                if target_interpretation:
                    # We already know which interpretation to edit
                    next_message = f"Editing prompt for **{target_interpretation['interpretation']['name']}** in **{target_interpretation['function_name']}**.\n\nPlease provide the new prompt in this format:\n**Message:** [your message]\n**Logic:** [your logic]\n\nOr just provide them on separate lines."
                    next_state = ConversationStatePhone.COLLECTING_PROMPT_UPDATE
                    extracted_data = json.dumps({
                        "interpretation_to_edit": target_interpretation,
                        "edit_type": edit_type
                    })
                else:
                    # Need to select interpretation - fetch fresh bot functions
                    all_bot_functions = await fetch_all_bot_functions_phone(bearer_token)
                    next_message = "Which interpretation's prompt would you like to edit? Please specify the interpretation name:"
                    next_state = ConversationStatePhone.SELECTING_INTERPRETATION
                    extracted_data = json.dumps({"bot_functions": all_bot_functions, "edit_type": edit_type})
            else:
                next_message = "Please choose either 'situation' or 'prompt' to edit."
                next_state = ConversationStatePhone.SELECTING_EDIT_TYPE
                extracted_data = conversations[message.user_id][-2]["extracted_data"]
            
            conversations[message.user_id].append({
                "role": "assistant",
                "content": next_message,
                "extracted_data": extracted_data,
                "state": next_state
            })
            save_conversations_phone(conversations)
            logger.info(f"Edit type selected: {edit_type if 'edit_type' in locals() else 'none'}")
            next_messages=strip_markdown(next_message)
            return ChatResponse(user_id=message.user_id, response=next_messages)

        # Handle interpretation selection - Step 3: Select which interpretation to edit
        if current_state == ConversationStatePhone.SELECTING_INTERPRETATION:
            try:
                stored_data_raw = conversations[message.user_id][-2]["extracted_data"]
                stored_data = json.loads(stored_data_raw) if stored_data_raw else {}
                
                # Handle case where stored_data might be a list (from old format) 
                if isinstance(stored_data, list):
                    # Convert old list format to new dict format
                    stored_data = {"bot_functions": stored_data, "edit_type": None}
                
                bot_functions_data = stored_data.get("bot_functions", [])
                edit_type = stored_data.get("edit_type")
                
                # Find interpretation by name
                interpretation_to_edit = find_interpretation_by_name_phone(bot_functions_data, message.message.strip())
                
                if interpretation_to_edit:
                    if edit_type == "situation":
                        next_message = f"Editing situation for **{interpretation_to_edit['interpretation']['name']}** in **{interpretation_to_edit['function_name']}**.\n\nPlease provide the new situation description:"
                        next_state = ConversationStatePhone.COLLECTING_SITUATION_UPDATE
                    elif edit_type == "prompt":
                        next_message = f"Editing prompt for **{interpretation_to_edit['interpretation']['name']}** in **{interpretation_to_edit['function_name']}**.\n\nPlease provide the new prompt in this format:\n**Message:** [your message]\n**Logic:** [your logic]\n\nOr just provide them on separate lines."
                        next_state = ConversationStatePhone.COLLECTING_PROMPT_UPDATE
                    else:
                        # No edit type specified, ask what to edit
                        next_message = f"What would you like to edit for **{interpretation_to_edit['interpretation']['name']}**?\n\n1. **Situation block** (trigger conditions)\n2. **Prompt block** (message and logic)\n\nPlease specify which one."
                        next_state = ConversationStatePhone.SELECTING_EDIT_TYPE
                    
                    conversations[message.user_id].append({
                        "role": "assistant",
                        "content": next_message,
                        "extracted_data": json.dumps({
                            "interpretation_to_edit": interpretation_to_edit,
                            "edit_type": edit_type
                        }),
                        "state": next_state
                    })
                    save_conversations_phone(conversations)
                    logger.info(f"Selected interpretation: {interpretation_to_edit['interpretation']['name']}")
                    next_messag=strip_markdown(next_message)
                    return ChatResponse(user_id=message.user_id, response=next_messag)
                else:
                    # Interpretation not found
                    interpretation_list = []
                    for func in bot_functions_data:
                        for interp in func.get("interpretations", []):
                            interpretation_list.append(f"• **{interp.get('name', 'Unnamed')}** (in {func.get('name', 'Unnamed')})")
                    
                    error_message = f"I couldn't find an interpretation named '{message.message.strip()}'. Available interpretations:\n\n" + "\n".join(interpretation_list) + "\n\nPlease specify the exact interpretation name:"
                    
                    conversations[message.user_id].append({
                        "role": "assistant",
                        "content": error_message,
                        "extracted_data": json.dumps(stored_data),  # Keep the corrected format
                        "state": ConversationStatePhone.SELECTING_INTERPRETATION
                    })
                    save_conversations_phone(conversations)
                    logger.info(f"Interpretation not found: {message.message.strip()}")
                    error_messag=strip_markdown(error_message)
                    return ChatResponse(user_id=message.user_id, response=error_messag)
                    
            except (json.JSONDecodeError, KeyError, IndexError) as e:
                logger.error(f"Error processing interpretation selection: {str(e)}")
                # Fallback: fetch fresh bot functions and restart the flow
                all_bot_functions = await fetch_all_bot_functions_phone(bearer_token)
                interpretations_message = generate_interpretations_list_message_phone(all_bot_functions)
                fallback_message = f"There was an issue processing your request. Let's start over.\n\n{interpretations_message}\n\nWhich interpretation would you like to edit?"
                
                conversations[message.user_id].append({
                    "role": "assistant",
                    "content": fallback_message,
                    "extracted_data": json.dumps(all_bot_functions),
                    "state": ConversationStatePhone.SELECTING_INTERPRETATION
                })
                save_conversations_phone(conversations)
                logger.info(f"Error processing interpretation selection: {str(e)}")
                fallback_messag=strip_markdown(fallback_message)
                return ChatResponse(user_id=message.user_id, response=fallback_messag)
            
        # Handle situation update - Step 4a: Update situation
        if current_state == ConversationStatePhone.COLLECTING_SITUATION_UPDATE:
            stored_data = json.loads(conversations[message.user_id][-2]["extracted_data"])
            interpretation_to_edit = stored_data["interpretation_to_edit"]
            interpretation_id = interpretation_to_edit["interpretation"]["_id"]
            
            # Fetch detailed bot function info
            detailed = await fetch_bot_function_detailed_phone(interpretation_id, bearer_token)
            
            if detailed and detailed.get("situation"):
                bot_function_detailed_id = detailed["_id"]
                situation_list = detailed.get("situation", [])
                situation_id = situation_list[0]["id"] if situation_list else None
                
                if situation_id:
                    new_situation_name = message.message.strip()
                    
                    # Start background task for updating situation
                    background_tasks.add_task(
                        update_situation_task_phone,
                        bot_function_detailed_id,
                        situation_id,
                        new_situation_name,
                        bearer_token
                    )
                    
                    success_message = f"✅ Situation update for **{interpretation_to_edit['interpretation']['name']}** has been initiated!\n\nNew situation: \"{new_situation_name}\"\n\nWhat would you like to do next? Add more functions, edit another, or list current functions?"
                    
                    conversations[message.user_id].append({
                        "role": "assistant",
                        "content": success_message,
                        "extracted_data": None,
                        "state": ConversationStatePhone.INITIAL
                    })
                    save_conversations_phone(conversations)
                    logger.info(f"Situation updated for {interpretation_to_edit['interpretation']['name']}")
                    success_messag=strip_markdown(success_message)
                    return ChatResponse(user_id=message.user_id, response=success_messag)
                else:
                    error_message = "No situation found to update. Please try again or contact support."
            else:
                error_message = "Could not fetch interpretation details. Please try again."
            
            conversations[message.user_id].append({
                "role": "assistant",
                "content": error_message,
                "extracted_data": None,
                "state": ConversationStatePhone.INITIAL
            })
            save_conversations_phone(conversations)
            logger.error(f"Failed to update situation for user {message.user_id}")
            error_messag=strip_markdown(error_message)
            return ChatResponse(user_id=message.user_id, response=error_messag)
        
        # Handle prompt update - Step 4b: Update prompt
        if current_state == ConversationStatePhone.COLLECTING_PROMPT_UPDATE:
            stored_data = json.loads(conversations[message.user_id][-2]["extracted_data"])
            interpretation_to_edit = stored_data["interpretation_to_edit"]
            interpretation_id = interpretation_to_edit["interpretation"]["_id"]
            
            # Parse message and logic from user input
            new_message, new_logic = parse_prompt_update_phone(message.message)
            
            if new_message and new_logic:
                # Try to get botFunctionDetailedId directly from interpretation first
                bot_function_detailed_id = interpretation_to_edit["interpretation"].get("botFunctionDetailedId")
                
                if not bot_function_detailed_id:
                    # Fallback: Fetch detailed bot function info
                    detailed = await fetch_bot_function_detailed_phone(interpretation_id, bearer_token)
                    if detailed:
                        bot_function_detailed_id = detailed["_id"]
                
                if bot_function_detailed_id:
                    # Start background task for updating prompt
                    background_tasks.add_task(
                        update_prompt_task_phone,
                        bot_function_detailed_id,
                        new_message,
                        new_logic,
                        bearer_token
                    )
                    
                    success_message = f"✅ Prompt update for **{interpretation_to_edit['interpretation']['name']}** has been initiated!\n\nNew message: \"{new_message}\"\nNew logic: \"{new_logic}\"\n\nWhat would you like to do next? Add more functions, edit another, or list current functions?"
                    
                    conversations[message.user_id].append({
                        "role": "assistant",
                        "content": success_message,
                        "extracted_data": None,
                        "state": ConversationStatePhone.INITIAL
                    })
                    save_conversations_phone(conversations)
                    logger.info(f"Prompt updated for {interpretation_to_edit['interpretation']['name']}")
                    success_messag=strip_markdown(success_message)
                    return ChatResponse(user_id=message.user_id, response=success_messag)
                else:
                    error_message = "Could not fetch interpretation details. Please try again."
            else:
                error_message = "Please provide both message and logic. Use this format:\n\n**Message:** [your message]\n**Logic:** [your logic]\n\nOr provide them on separate lines."
            
            conversations[message.user_id].append({
                "role": "assistant",
                "content": error_message,
                "extracted_data": conversations[message.user_id][-2]["extracted_data"],
                "state": ConversationStatePhone.COLLECTING_PROMPT_UPDATE
            })
            save_conversations_phone(conversations)
            logger.warning(f"Failed to parse prompt update for user {message.user_id}")
            error_messag=strip_markdown(error_message)
            return ChatResponse(user_id=message.user_id, response=error_messag)
        
        # Handle creation flow with background tasks
        if current_state == ConversationStatePhone.COLLECTING_MESSAGE_BLOCK and await check_message_block_complete_phone(conversations[message.user_id]):
            message_block = await extract_message_block_data_phone(conversations[message.user_id])
            if message_block and user_intent == "create":
                # Start background task
                background_tasks.add_task(post_conversation_block_task_phone, message_block, bearer_token)
                
                response_message = "✅ Message block is being saved in the background! What's the first bot function (name, sub-function, trigger, prompt, action)?"
                new_state = ConversationStatePhone.COLLECTING_BOT_FUNCTIONS
            else:
                response_message = "Please provide both a greeting message and behavioral logic for your bot."
                new_state = current_state
            
            conversations[message.user_id].append({
                "role": "assistant",
                "content": response_message,
                "extracted_data": None,
                "state": new_state
            })
            save_conversations_phone(conversations)
            logger.info(f"Message block saved for user {message.user_id}")
            response_messag=strip_markdown(response_message)
            return ChatResponse(user_id=message.user_id, response=response_messag)
        
        if current_state == ConversationStatePhone.COLLECTING_BOT_FUNCTIONS and await check_single_bot_function_complete_phone(conversations[message.user_id]):
            bot_function = await extract_single_bot_function_data_phone(conversations[message.user_id])
            if bot_function:
                fn_name = bot_function.get("bot_function", "")
                sub_fn_name = bot_function.get("sub_bot_function", "")
                if function_exists_phone(all_bot_functions, fn_name, sub_fn_name):
                    response_message = f"Function '{fn_name}' with interpretation '{sub_fn_name}' already exists. Would you like to edit it or create a different one?"
                    new_state = ConversationStatePhone.LISTING_INTERPRETATIONS
                    extracted_data = json.dumps(all_bot_functions)
                elif user_intent == "create":
                    # Start background task
                    background_tasks.add_task(post_single_bot_function_task_phone, bot_function, bearer_token)
                    
                    response_message = f"✅ Function '{fn_name}' is being created in the background! Would you like to add another function or are you done?"
                    new_state = ConversationStatePhone.BOT_FUNCTION_POSTED
                    extracted_data = json.dumps({"bot_functions": [bot_function]})
                else:
                    response_message = f"Ready to create function: **{fn_name}** - **{sub_fn_name}**. Should I proceed?"
                    new_state = ConversationStatePhone.WAITING_FUNCTION_CONFIRMATION
                    extracted_data = json.dumps(bot_function)
            else:
                response_message = "Please provide complete function details: name, sub-function, trigger situation, prompt message, and action logic."
                new_state = current_state
                extracted_data = None
            
            conversations[message.user_id].append({
                "role": "assistant",
                "content": response_message,
                "extracted_data": extracted_data,
                "state": new_state
            })
            save_conversations_phone(conversations)
            response_messag=strip_markdown(response_message)
            return ChatResponse(user_id=message.user_id, response=response_messag)
        
        if current_state == ConversationStatePhone.BOT_FUNCTION_POSTED and user_intent == "done":
            response_message = await generate_completion_message_phone()
            new_state = ConversationStatePhone.COMPLETED
            conversations[message.user_id].append({
                "role": "assistant",
                "content": response_message,
                "extracted_data": None,
                "state": new_state
            })
            save_conversations_phone(conversations)
            response_messag=strip_markdown(response_message)
            return ChatResponse(user_id=message.user_id, response=response_messag)
        
        if current_state == ConversationStatePhone.WAITING_FUNCTION_CONFIRMATION and user_intent == "create":
            bot_function = await extract_single_bot_function_data_phone(conversations[message.user_id])
            if bot_function:
                fn_name = bot_function.get("bot_function", "")
                sub_fn_name = bot_function.get("sub_bot_function", "")
                if function_exists_phone(all_bot_functions, fn_name, sub_fn_name):
                    response_message = f"Function '{fn_name}' with interpretation '{sub_fn_name}' already exists. Would you like to edit it or create a different one?"
                    new_state = ConversationStatePhone.LISTING_INTERPRETATIONS
                    extracted_data = json.dumps(all_bot_functions)
                else:
                    # Start background task
                    background_tasks.add_task(post_single_bot_function_task_phone, bot_function, bearer_token)
                    
                    response_message = f"✅ Function '{fn_name}' is being created in the background! Would you like to add another function or are you done?"
                    new_state = ConversationStatePhone.BOT_FUNCTION_POSTED
                    extracted_data = json.dumps({"bot_functions": [bot_function]})
            else:
                response_message = "Function details are missing. Let's try again with complete information."
                new_state = ConversationStatePhone.COLLECTING_BOT_FUNCTIONS
                extracted_data = None
            
            conversations[message.user_id].append({
                "role": "assistant",
                "content": response_message,
                "extracted_data": extracted_data,
                "state": new_state
            })
            save_conversations_phone(conversations)
            response_messag=strip_markdown(response_message)
            return ChatResponse(user_id=message.user_id, response=response_messag)
            return ChatResponse(user_id=message.user_id, response=response_message)
        
        # Handle create requests from initial state
        if current_state == ConversationStatePhone.INITIAL and (user_intent == "create" or is_create_request_phone(message.message)):
            if "message" in message.message.lower() or "block" in message.message.lower():
                response_message = "Great! Let's create a message block. Please provide the greeting message and behavioral logic for your bot."
                new_state = ConversationStatePhone.COLLECTING_MESSAGE_BLOCK
            else:
                response_message = "What would you like to create? A message block or a bot function?"
                new_state = ConversationStatePhone.INITIAL
            
            conversations[message.user_id].append({
                "role": "assistant",
                "content": response_message,
                "extracted_data": None,
                "state": new_state
            })
            save_conversations_phone(conversations)
            response_messag=strip_markdown(response_message)
            return ChatResponse(user_id=message.user_id, response=response_messag)
        
        # Default response generation
        extracted_data = get_latest_extracted_data_phone(conversations[message.user_id])
        new_system_prompt = create_dynamic_system_prompt_phone(current_state, extracted_data)
        conversations[message.user_id][0]["content"] = new_system_prompt
        
        from chatbot_receptionist import client
        response = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: client.chat.completions.create(
                model="gpt-4o",
                messages=conversations[message.user_id][-5:],
                max_tokens=150,
                temperature=0.7
            )
        )
        assistant_message = response.choices[0].message.content
        
        conversations[message.user_id].append({
            "role": "assistant",
            "content": assistant_message,
            "extracted_data": json.dumps(extracted_data) if extracted_data else None,
            "state": current_state
        })
        save_conversations_phone(conversations)
        logger.info(f"Generated default response for user {message.user_id}")
        
        assistant_messag=strip_markdown(assistant_message)
        return ChatResponse(user_id=message.user_id, response=assistant_messag)
    
    except Exception as e:
        logger.error(f"Error processing chat request: {str(e)}", exc_info=True)
        error_message = await generate_error_message_phone(str(e))
        error_messag=strip_markdown(error_message)
        return ChatResponse(user_id=message.user_id, response=error_messag)

@app.post("/research-chat", tags=["Chatbots"], summary="Research home chatbot", response_model=ChatResponse)
async def crm_chat(message: ChatMessage):
    try:
        # Validate input
        if not message.user_id.strip() or not message.message.strip():
            logger.error(f"Invalid input: user_id={message.user_id}, message={message.message}")
            raise HTTPException(status_code=422, detail="user_id and message cannot be empty")
        
        response = research_assistant(message.message, userid=message.user_id)
        cleaned_response=strip_markdown(response)
        return ChatResponse(
            user_id=message.user_id,
            response=cleaned_response
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="debug")