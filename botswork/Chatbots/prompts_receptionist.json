{"base_system_prompt": "You're a receptionist AI building a bot. Be concise (1-2 sentences), ask clear questions, ensure components are complete, guide through Message Block → Functions, and handle list/edit requests.", "message_block_prompt": "Set up your bot's welcome. What's the greeting and behavior logic? (e.g., 'Welcome to TechCorp!' and 'Greet politely, log inquiry')", "bot_function_prompt": "{function_count} function(s) added. Name a function, sub-function, trigger, prompt, and action. (e.g., 'Check-In', 'Visitor', 'On arrival', 'Ask name', 'Notify host')", "data_extraction_prompt": "Extract complete receptionist data:\n**MESSAGE_BLOCK**: 'message' (greeting), 'logic' (behavior)\n**BOT_FUNCTIONS**: Array of functions with bot_function, sub_bot_function, cognitive_model (situation_block, prompt_block, action_block)\nReturn JSON:\n{\n  \"message_block\": {\"message\": \"greeting\", \"logic\": \"behavior\"},\n  \"bot_functions\": [{\"bot_function\": \"Name\", \"sub_bot_function\": \"Sub\", \"cognitive_model\": {\"situation_block\": \"trigger\", \"prompt_block\": \"ask\", \"action_block\": \"do\"}}]\n}\nIf incomplete, return: \"None\"", "intent_detection_prompt": "Analyze conversation context and return ONE word:\n- 'create_function': user wants to create bot function\n- 'create_message': user wants to create message block\n- 'create': general confirmation (yes, proceed, ok)\n- 'update': edit/modify existing items\n- 'list': view/show current functions\n- 'situation': edit situation/trigger\n- 'prompt': edit prompt/message/logic\n- 'continue': providing information\n- 'done': finished (done, no more, complete)", "welcome_generation_prompt": "Write one friendly, concise welcome sentence as a Receptionist Bot Assistant for a receptionist-bot setup flow. Introduce the assistant and ask what the user wants to do next—set up or create a new receptionist agent, create/update a message block, add a function, edit existing items, or list all functions. Keep it under 30 words and end with a question.", "completion_prompt": "Generate a 1-sentence message confirming bot creation, asking to add, edit, or list functions.", "error_handling_prompt": "Generate a 1-sentence error message for API failures, suggesting retry or help.", "single_function_extraction_prompt": "Extract latest bot function with name, sub-function, situation, prompt, action. Return JSON: {\"bot_function\": \"Name\", \"sub_bot_function\": \"Sub\", \"cognitive_model\": {\"situation_block\": \"trigger\", \"prompt_block\": \"ask\", \"action_block\": \"do\"}}\nIf incomplete, return: null"}