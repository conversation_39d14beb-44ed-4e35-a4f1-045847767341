import json
import os
from typing import Dict, List, Any
from pathlib import Path

class ToolsConfigLoader:
    """Load and manage tools configuration from JSON file"""
    
    def __init__(self, config_path: str = "crm_tools_config.json"):
        self.config_path = config_path
        self._config = None
        self._tools_cache = None
        self._system_prompt_cache = None
        
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        if self._config is None:
            try:
                config_file = Path(self.config_path)
                if config_file.exists():
                    with open(config_file, 'r', encoding='utf-8') as f:
                        self._config = json.load(f)
                else:
                    raise FileNotFoundError(f"Configuration file {self.config_path} not found")
            except Exception as e:
                raise Exception(f"Failed to load tools configuration: {e}")
        return self._config
    
    def get_openai_tools(self) -> List[Dict[str, Any]]:
        """Convert configuration to OpenAI tools format"""
        if self._tools_cache is not None:
            return self._tools_cache
            
        config = self.load_config()
        tools = []
        
        try:
            tool_registry = config["crm_tools"]["tool_registry"]
            
            for category_key, category in tool_registry.items():
                for tool_config in category["tools"]:
                    openai_tool = {
                        "type": "function",
                        "function": {
                            "name": tool_config["function_name"],
                            "description": tool_config["description"],
                            "parameters": tool_config["parameters"]
                        }
                    }
                    tools.append(openai_tool)
            
            self._tools_cache = tools
            return tools
            
        except KeyError as e:
            raise Exception(f"Invalid configuration structure: missing {e}")
    
    def get_tool_handler_mapping(self) -> Dict[str, str]:
        """Get mapping of tool names to handler functions"""
        config = self.load_config()
        return config["crm_tools"]["tool_mappings"]
    
    def get_system_prompt(self) -> str:
        """Generate system prompt from configuration"""
        if self._system_prompt_cache is not None:
            return self._system_prompt_cache
            
        config = self.load_config()
        prompt_config = config["crm_tools"]["system_prompt_config"]
        
        # Build the system prompt
        base_prompt = prompt_config["base_prompt"]
        
        # Add available tools description
        tools_description = self._build_tools_description()
        
        # Add guidelines
        guidelines = "\n".join([f"- {guideline}" for guideline in prompt_config["guidelines"]])
        
        # Add response format
        response_format = []
        for format_type, description in prompt_config["response_format"].items():
            response_format.append(f"- For {format_type.replace('_', ' ')}: {description}")
        
        system_prompt = f"""{base_prompt} You have access to several tools that allow you to:

1. **Customer Profile Management**: Retrieve and analyze customer profiles, including recent entries and summaries
2. **CRM Function Management**: List existing functions, create new functions, and manage function situations
3. **Situation Management**: Add and update trigger conditions for CRM functions

Key Guidelines:
{guidelines}

{tools_description}

Response Format:
{chr(10).join(response_format)}

Always execute the appropriate tool based on user intent without asking for confirmation or additional details unless absolutely necessary."""

        self._system_prompt_cache = system_prompt
        return system_prompt
    
    def _build_tools_description(self) -> str:
        """Build tools description from configuration"""
        config = self.load_config()
        tool_registry = config["crm_tools"]["tool_registry"]
        
        tools_list = []
        tool_counter = 1
        
        for category_key, category in tool_registry.items():
            for tool_config in category["tools"]:
                tools_list.append(f"{tool_counter}. `{tool_config['function_name']}` - {tool_config['title']}")
                tool_counter += 1
        
        return f"Available Tools:\n{chr(10).join(tools_list)}"
    
    def get_environment_config(self) -> Dict[str, Any]:
        """Get environment configuration"""
        config = self.load_config()
        return config["crm_tools"]["environment"]
    
    def reload_config(self):
        """Force reload configuration from file"""
        self._config = None
        self._tools_cache = None
        self._system_prompt_cache = None

# Global instance
tools_config = ToolsConfigLoader()