{"crm_tools": {"schema_version": "1.0.0", "environment": {"name": "prod", "api_model": "gpt-4o", "max_tokens": 300, "temperature": 0.7}, "tool_registry": {"customer_management": {"id": "customer_management", "title": "Customer Management Tools", "desc": "Tools for retrieving and managing customer profile information", "tools": [{"id": "get_customer_profiles", "function_name": "get_customer_profiles", "title": "Get Customer Profiles", "description": "Retrieve customer profiles - recent (last 3 days), all profiles, or profile summaries", "handler": "get_customer_profiles_tool", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User ID"}, "mode": {"type": "string", "enum": ["recent", "all", "summary"], "description": "Type of profiles to retrieve"}}, "required": ["user_id", "mode"]}}]}, "crm_function_management": {"id": "crm_function_management", "title": "CRM Function Management Tools", "desc": "Tools for managing CRM functions and their configurations", "tools": [{"id": "list_crm_functions", "function_name": "list_crm_functions", "title": "List CRM Functions", "description": "List existing CRM functions and their configurations", "handler": "list_crm_functions_tool", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User ID"}}, "required": ["user_id"]}}, {"id": "create_crm_function", "function_name": "create_crm_function", "title": "Create CRM Function", "description": "Create a new CRM function with trigger situations", "handler": "create_crm_function_tool", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User ID"}, "function_name": {"type": "string", "description": "Name of the CRM function"}, "situation_description": {"type": "string", "description": "When this function should trigger"}}, "required": ["user_id", "function_name", "situation_description"]}}]}, "situation_management": {"id": "situation_management", "title": "Situation Management Tools", "desc": "Tools for managing trigger situations within CRM functions", "tools": [{"id": "add_situation", "function_name": "add_situation", "title": "Add Situation", "description": "Add a new trigger situation to an existing CRM function", "handler": "add_situation_tool", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User ID"}, "function_name": {"type": "string", "description": "Name of the existing CRM function"}, "situation_description": {"type": "string", "description": "New trigger situation to add"}}, "required": ["user_id", "function_name", "situation_description"]}}, {"id": "update_situation", "function_name": "update_situation", "title": "Update Situation", "description": "Update an existing trigger situation for a CRM function", "handler": "update_situation_tool", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User ID"}, "function_name": {"type": "string", "description": "Name of the CRM function"}, "old_situation": {"type": "string", "description": "Current situation to update"}, "new_situation": {"type": "string", "description": "New situation description"}}, "required": ["user_id", "function_name", "old_situation", "new_situation"]}}]}}, "tool_mappings": {"get_customer_profiles_tool": "crm_tools.get_customer_profiles_tool", "list_crm_functions_tool": "crm_tools.list_crm_functions_tool", "create_crm_function_tool": "crm_tools.create_crm_function_tool", "add_situation_tool": "crm_tools.add_situation_tool", "update_situation_tool": "crm_tools.update_situation_tool"}, "system_prompt_config": {"base_prompt": "You are an advanced CRM Assistant designed to help users manage their customer relationship management functions intelligently.", "guidelines": ["Keep responses concise and direct (2-3 sentences max unless complex data is presented)", "The user_id is already provided in the system - never ask for it", "Focus on actionable information and clear next steps", "Use tools automatically based on user intent without explaining the process"], "response_format": {"data_retrieval": "Present key information concisely", "actions": "Confirm what was done and suggest next steps", "errors": "Provide brief solution-oriented guidance"}}}}