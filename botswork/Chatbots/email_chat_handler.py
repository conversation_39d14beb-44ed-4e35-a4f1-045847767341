import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from openai import AsyncOpenAI
from uuid import uuid4
import os
from dotenv import load_dotenv

from models import ChatMessage, ChatResponse
from email_tools import (
    list_email_functions_tool, 
    create_email_function_tool,
    add_email_situation_tool,
    update_email_situation_tool,
    validate_user_registration,
    send_email_to_api_tool
)

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
X_API_KEY = os.getenv("X_API_KEY")
BASE_URL = os.getenv("BASE_URL")

# Initialize OpenAI client
client = AsyncOpenAI(api_key=OPENAI_API_KEY)

# Load system prompt from JSON file
try:
    with open("email_prompts.json", "r") as file:
        prompts = json.load(file)
        SYSTEM_PROMPT = prompts["system_prompt"]
except FileNotFoundError:
    logger.error("email_prompts.json file not found")
    raise Exception("Could not load email_prompts.json. Please ensure the file exists in the correct directory.")
except KeyError:
    logger.error("Key 'system_prompt' not found in email_prompts.json")
    raise Exception("Invalid email_prompts.json format. Ensure it contains a 'system_prompt' key.")
except json.JSONDecodeError as e:
    logger.error(f"Invalid JSON in email_prompts.json: {e}")
    raise Exception("Invalid JSON format in email_prompts.json.")

# In-memory stores
_conversation_history = {}  # {user_id: {session_id: [messages]}}

# Tool definitions for OpenAI
TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "list_email_functions",
            "description": "List existing email bot functions and their configurations",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_email_function",
            "description": "Create a new email bot function with situations for filtering emails",
            "parameters": {
                "type": "object",
                "properties": {
                    "function_name": {"type": "string", "description": "Name of the email bot function"},
                    "situation_description": {"type": "string", "description": "New situation description for the new bot functions for filtering emails"}
                },
                "required": ["function_name", "situation_description"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "add_email_situation",
            "description": "Add a new situation to an existing email bot function for filtering emails",
            "parameters": {
                "type": "object",
                "properties": {
                    "function_name": {"type": "string", "description": "Name of the existing email bot function"},
                    "situation_description": {"type": "string", "description": "New situation to add to filter emails"}
                },
                "required": ["function_name", "situation_description"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "update_email_situation",
            "description": "Update an existing situation for an email bot function for filtering emails",
            "parameters": {
                "type": "object",
                "properties": {
                    "function_name": {"type": "string", "description": "Name of the email bot function"},
                    "old_situation": {"type": "string", "description": "Current situation to update"},
                    "new_situation": {"type": "string", "description": "New situation description"}
                },
                "required": ["function_name", "old_situation", "new_situation"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "send_email_to_api",
            "description": "Send an email draft to the mail API",
            "parameters": {
                "type": "object",
                "properties": {
                    "draft_message": {"type": "string", "description": "The email draft in the format: --- Subject: [Subject Line] To: [Recipient Email] Body: [Email body content] ---"}
                },
                "required": ["draft_message"]
            }
        }
    }
]

async def handle_email_chat(message: ChatMessage) -> ChatResponse:
    """Main handler for Email chat requests using GPT-4 with tools"""
    try:
        # Validate user registration
        validation = await validate_user_registration(message.user_id)
        if not validation["valid"]:
            error_message = f"I'm having trouble authenticating your request: {validation['error']}. Please check your user ID or contact support if this persists."
            logger.error(f"Invalid user_id {message.user_id}: {validation['error']}")
            return ChatResponse(user_id=message.user_id, response=error_message)

        user_id = message.user_id
        user_message = message.message
        user_name = validation.get("user_name", "there")

        # Handle session management
        session_id = f"{user_id}:{uuid4()}" if user_message.lower() in ["reset", "start over", "new session"] else \
                     next((k for k in _conversation_history.get(user_id, {}).keys()), f"{user_id}:{uuid4()}")

        # Initialize conversation history
        if user_id not in _conversation_history:
            _conversation_history[user_id] = {}
        if session_id not in _conversation_history[user_id]:
            _conversation_history[user_id][session_id] = []
        
        # Build conversation context
        conversation_history = _conversation_history[user_id][session_id]
        messages = [{"role": "system", "content": SYSTEM_PROMPT}] + conversation_history + [{"role": "user", "content": user_message}]

        # First GPT call to understand intent and use tools
        response = await client.chat.completions.create(
            model="gpt-4o",
            messages=messages,
            tools=TOOLS,
            tool_choice="auto",
            temperature=0.7
        )

        assistant_message = response.choices[0].message
        
        # Handle tool calls
        if assistant_message.tool_calls:
            tool_results = []
            for tool_call in assistant_message.tool_calls:
                function_name = tool_call.function.name
                args = json.loads(tool_call.function.arguments)
                logger.info(f"Tool call: {function_name} with args: {args}")
                
                # Ensure user_id is correct
                args["user_id"] = user_id
                
                # Execute the appropriate tool
                if function_name == "list_email_functions":
                    result = await list_email_functions_tool(**args)
                elif function_name == "create_email_function":
                    result = await create_email_function_tool(**args)
                elif function_name == "add_email_situation":
                    result = await add_email_situation_tool(**args)
                elif function_name == "update_email_situation":
                    result = await update_email_situation_tool(**args)
                elif function_name == "send_email_to_api":
                    result = await send_email_to_api_tool(**args)
                else:
                    result = {"error": f"Unknown function: {function_name}"}
                
                tool_results.append({
                    "tool_call_id": tool_call.id,
                    "result": result
                })

            # Add assistant message with tool calls to history
            messages.append({
                "role": "assistant",
                "content": assistant_message.content or "",
                "tool_calls": [tc.model_dump() for tc in assistant_message.tool_calls]
            })

            # Add tool results to messages
            for tool_result in tool_results:
                messages.append({
                    "role": "tool",
                    "content": json.dumps(tool_result["result"]),
                    "tool_call_id": tool_result["tool_call_id"]
                })

            # Get final response from GPT
            final_response = await client.chat.completions.create(
                model="gpt-4o",
                messages=messages,
                temperature=0.7
            )
            
            bot_response = final_response.choices[0].message.content
            bot_response = bot_response.replace("*", "")

        else:
            # Direct response without tool use (for email drafts or general conversation)
            bot_response = assistant_message.content
            if "[Your Name]" in bot_response:
                bot_response = bot_response.replace("*", "")
                bot_response = bot_response.replace("[Your Name]", user_name)

        # Update conversation history
        _conversation_history[user_id][session_id].extend([
            {"role": "user", "content": user_message},
            {"role": "assistant", "content": bot_response}
        ])

        # Limit history to last 20 messages
        _conversation_history[user_id][session_id] = _conversation_history[user_id][session_id][-20:]

        return ChatResponse(user_id=user_id, response=bot_response)

    except Exception as e:
        logger.error(f"Error in handle_email_chat: {e}")
        error_response = "I apologize, but I encountered an issue processing your request. Please try again or contact support if the problem persists."
        return ChatResponse(user_id=message.user_id, response=error_response)