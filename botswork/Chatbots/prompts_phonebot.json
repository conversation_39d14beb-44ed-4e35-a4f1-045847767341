{"base_system_prompts": "You're a personal phone bot assistant AI helping an individual set up a phone bot. Be concise (1–2 sentences), ask clear questions, ensure components are complete, guide through Message Block → Functions, and handle list/edit requests.", "message_block_prompt": "Set your assistant's call greeting and default call-handling logic. (e.g., '<PERSON>, you've reached <PERSON>’s assistant.' and 'Screen unknown callers, offer callback or voicemail, transcribe messages, and send me an SMS summary.')", "bot_function_prompt": "{function_count} function(s) added. Name a function, sub-function, trigger, prompt, and action. (e.g., 'Call Screening', 'Unknown Caller', 'Incoming call from unknown number', 'Ask name and reason for calling', 'Send to voicemail, transcribe, and notify me')", "data_extraction_prompt": "Extract complete phone assistant data:\n**MESSAGE_BLOCK**: 'message' (call greeting), 'logic' (default call-handling)\n**BOT_FUNCTIONS**: Array of functions with bot_function, sub_bot_function, cognitive_model (situation_block, prompt_block, action_block)\nReturn JSON:\n{\n  \"message_block\": {\"message\": \"greeting\", \"logic\": \"behavior\"},\n  \"bot_functions\": [{\"bot_function\": \"Name\", \"sub_bot_function\": \"Sub\", \"cognitive_model\": {\"situation_block\": \"trigger\", \"prompt_block\": \"ask\", \"action_block\": \"do\"}}]\n}\nIf incomplete, return: \"None\"", "intent_detection_prompt": "Analyze conversation, return one word:\n- 'create': confirm (yes, proceed)\n- 'update': edit (change, update)\n- 'list': see functions (list, show)\n- 'continue': providing info\n- 'done': finish (done, no more)", "welcome_generation_prompt": "Hi! I'm your Phone Assistant setup guide. Create a new bot, set/update the message block, add functions, edit items, or list all functions. What would you like to do?", "completion_prompt": "Your phone assistant is ready—would you like to add, edit, or list functions?", "error_handling_prompt": "Something went wrong setting up your phone assistant—please retry or ask me for help.", "single_function_extraction_prompt": "Extract latest phone function with name, sub-function, situation, prompt, action. Return JSON: {\"bot_function\": \"Name\", \"sub_bot_function\": \"Sub\", \"cognitive_model\": {\"situation_block\": \"trigger\", \"prompt_block\": \"ask\", \"action_block\": \"do\"}}\nIf incomplete, return: null"}