
import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import httpx
import os
from dotenv import load_dotenv

# Set up logging
logger = logging.getLogger(__name__)
# Load environment variables  
load_dotenv()
X_API_KEY = os.getenv("X_API_KEY")
BASE_URL = os.getenv("BASE_URL")

# Cache for tokens and requests
_token_cache = {}
_request_cache = {}

async def fetch_bearer_token(user_id: str) -> Optional[Dict]:
    """Fetch bearer token and userName from the auth/user/token API."""
    if user_id in _token_cache:
        return _token_cache[user_id]
    
    url = f"{BASE_URL}/auth/user/token?userId={user_id}"
    headers = {"accept": "*/*", "X-API-Key": X_API_KEY}
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, headers=headers, timeout=10.0)
            response.raise_for_status()
            data = response.json()
            
            if data.get("status") == "SUCCESS" and isinstance(data.get("result"), dict):
                token = data["result"].get("token")
                user_name = data["result"].get("userName", "User")
                if not token:
                    return None
                    
                auth_data = {"token": token, "user_name": user_name}
                _token_cache[user_id] = auth_data
                return auth_data
                
            return None
            
        except httpx.HTTPStatusError as e:
            return None
        except httpx.RequestError as e:
            return None

async def get_customer_profiles_tool(user_id: str, mode: str) -> Dict:
    """Get customer profiles - recent, all, or summary"""
    try:
        auth_data = await fetch_bearer_token(user_id)
        if not auth_data:
            return {"error": "Authentication failed"}
        
        url = f"{BASE_URL}/crm/getcustomerprofile/{user_id}"
        headers = {
            "Authorization": f"Bearer {auth_data['token']}",
            "Content-Type": "application/json",
            "accept": "*/*"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers, timeout=10.0)
            response.raise_for_status()
            data = response.json()
            
            if data.get("status") != "SUCCESS" or "result" not in data:
                return {"error": "Failed to fetch profiles", "details": data}
            
            profiles = data["result"]
            
            # Sort by creation date (most recent first)
            sorted_profiles = sorted(
                profiles,
                key=lambda x: datetime.fromisoformat(x["createdAt"].replace("Z", "+00:00")),
                reverse=True
            )
            
            if mode == "recent":
                # Last 3 days
                three_days_ago = datetime.utcnow() - timedelta(days=3)
                recent_profiles = [
                    p for p in sorted_profiles
                    if datetime.fromisoformat(p["createdAt"].replace("Z", "+00:00")).replace(tzinfo=None) >= three_days_ago
                ]
                return {
                    "profiles": recent_profiles or sorted_profiles[:2],  # If no recent, return 2 most recent
                    "mode": "recent",
                    "count": len(recent_profiles) if recent_profiles else 2
                }
            
            elif mode == "all":
                return {
                    "profiles": sorted_profiles[:10],  # Limit to 10 most recent
                    "mode": "all", 
                    "count": len(sorted_profiles[:10])
                }
            
            elif mode == "summary":
                summary_profiles = []
                for profile in sorted_profiles[:10]:
                    summary = profile.get("profile_summary", {})
                    summary_profiles.append({
                        "name": profile.get("name", "Unknown"),
                        "email": profile.get("email", [None])[0],
                        "summary": summary.get("summary", "No summary available"),
                        "current_step": summary.get("current_step", "N/A"),
                        "status": summary.get("status", "N/A"),
                        "next_step": summary.get("next_step", "N/A"),
                        "created_at": profile.get("createdAt")
                    })
                
                return {
                    "profiles": summary_profiles,
                    "mode": "summary",
                    "count": len(summary_profiles)
                }
                
    except Exception as e:
        return {"error": str(e)}

async def list_crm_functions_tool(user_id: str) -> Dict:
    """List existing CRM functions and their configurations"""
    try:
        auth_data = await fetch_bearer_token(user_id)
        if not auth_data:
            return {"error": "Authentication failed"}
        
        url = f"{BASE_URL}/crm-bot-functions/list"
        headers = {
            "Authorization": f"Bearer {auth_data['token']}",
            "accept": "*/*"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers, timeout=10.0)
            response.raise_for_status()
            data = response.json()
            
            if data.get("status") != "SUCCESS" or "result" not in data:
                return {"error": "Failed to fetch functions", "details": data}
            
            functions = []
            for item in data["result"]:
                function_info = {
                    "name": item["name"],
                    "id": item["_id"],
                    "interpretations": item.get("interpretations", []),
                    "situations": []
                }
                
                # Get detailed info including situations
                for interpretation in item.get("interpretations", []):
                    try:
                        detail_url = f"{BASE_URL}/crm-bot-function-detailed?interpretationId={interpretation['_id']}"
                        detail_response = await client.get(detail_url, headers=headers, timeout=10.0)
                        if detail_response.status_code == 200:
                            detail_data = detail_response.json()
                            if detail_data.get("status") == "SUCCESS" and "result" in detail_data:
                                situations = detail_data["result"].get("situation", [])
                                function_info["situations"].extend([s["name"] for s in situations])
                    except Exception as e:
                        logger.warning(f"Failed to get details for interpretation {interpretation['_id']}: {e}")
                
                functions.append(function_info)
            
            return {
                "functions": functions,
                "count": len(functions)
            }
            
    except Exception as e:
        return {"error": str(e)}

async def create_crm_function_tool(user_id: str, function_name: str, situation_description: str) -> Dict:
    """Create a new CRM function with trigger situation"""
    try:
        auth_data = await fetch_bearer_token(user_id)
        if not auth_data:
            return {"error": "Authentication failed"}
        
        headers = {
            "Authorization": f"Bearer {auth_data['token']}",
            "Content-Type": "application/json",
            "accept": "*/*"
        }
        
        # Clean function name
        clean_name = function_name.strip()
        value = clean_name.lower().replace(" ", "_").replace("-", "_")
        
        async with httpx.AsyncClient() as client:
            # 1. Create the function
            function_payload = {"value": value, "name": clean_name}
            response = await client.post(f"{BASE_URL}/crm-bot-functions", headers=headers, json=function_payload, timeout=10.0)
            response.raise_for_status()
            function_data = response.json()
            
            if function_data.get("status") != "SUCCESS":
                return {"error": "Failed to create function", "details": function_data}
            
            # 2. Create interpretation
            interpretation_payload = {"botFunctionsValue": value, "value": value, "name": clean_name}
            response = await client.post(f"{BASE_URL}/crm-bot-functions/interpretation", headers=headers, json=interpretation_payload, timeout=10.0)
            response.raise_for_status()
            interpretation_data = response.json()
            
            if interpretation_data.get("status") != "SUCCESS":
                return {"error": "Failed to create interpretation", "details": interpretation_data}
            
            interpretation_id = interpretation_data["result"]["_id"]
            
            # 3. Get detailed function ID
            response = await client.get(f"{BASE_URL}/crm-bot-function-detailed?interpretationId={interpretation_id}", headers=headers, timeout=10.0)
            response.raise_for_status()
            detailed_data = response.json()
            
            if detailed_data.get("status") != "SUCCESS":
                return {"error": "Failed to get detailed function", "details": detailed_data}
            
            detailed_id = detailed_data["result"]["_id"]
            situations = detailed_data["result"].get("situation", [])
            
            # Add the new situation (since old_situation is not defined, we use situation_description to add)
            situation_payload = {"botFunctionDetailedId": detailed_id, "name": situation_description}
            response = await client.post(f"{BASE_URL}/crm-bot-function-detailed/situation", headers=headers, json=situation_payload, timeout=10.0)
            response.raise_for_status()
            situation_data = response.json()
            
            if situation_data.get("status") != "SUCCESS":
                return {"error": "Failed to add situation", "details": situation_data}
            
            return {
                "success": True,
                "function_name": function_name,
                "new_situation": situation_description
            }
            
    except Exception as e:
        return {"error": str(e)}

# Cache management
def clear_caches():
    """Clear all caches"""
    global _token_cache, _request_cache
    _token_cache.clear()
    _request_cache.clear()

async def add_situation_tool(user_id: str, function_name: str, situation_description: str) -> Dict:
    """Add a new situation to an existing function"""
    try:
        # First, get the function details
        functions_result = await list_crm_functions_tool(user_id)
        if "error" in functions_result:
            return functions_result
        
        # Find the matching function
        target_function = None
        for func in functions_result["functions"]:
            if func["name"].lower() == function_name.lower():
                target_function = func
                break
        
        if not target_function:
            return {"error": f"Function '{function_name}' not found"}
        
        if not target_function["interpretations"]:
            return {"error": f"No interpretations found for function '{function_name}'"}
        
        auth_data = await fetch_bearer_token(user_id)
        if not auth_data:
            return {"error": "Authentication failed"}
        
        headers = {
            "Authorization": f"Bearer {auth_data['token']}",
            "Content-Type": "application/json",
            "accept": "*/*"
        }
        
        # Get detailed function ID
        interpretation_id = target_function["interpretations"][0]["_id"]
        
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/crm-bot-function-detailed?interpretationId={interpretation_id}", headers=headers, timeout=10.0)
            response.raise_for_status()
            detailed_data = response.json()
            
            if detailed_data.get("status") != "SUCCESS":
                return {"error": "Failed to get detailed function", "details": detailed_data}
            
            detailed_id = detailed_data["result"]["_id"]
            
            # Add the new situation
            situation_payload = {"botFunctionDetailedId": detailed_id, "name": situation_description}
            response = await client.post(f"{BASE_URL}/crm-bot-function-detailed/situation", headers=headers, json=situation_payload, timeout=10.0)
            response.raise_for_status()
            situation_data = response.json()
            
            if situation_data.get("status") != "SUCCESS":
                return {"error": "Failed to add situation", "details": situation_data}
            
            return {
                "success": True,
                "function_name": function_name,
                "new_situation": situation_description
            }
            
    except Exception as e:
        return {"error": str(e)}

async def update_situation_tool(user_id: str, function_name: str, old_situation: str, new_situation: str) -> Dict:
    """Update an existing situation for a CRM function"""
    try:
        # Get function details
        functions_result = await list_crm_functions_tool(user_id)
        if "error" in functions_result:
            return functions_result
        
        # Find the matching function
        target_function = None
        for func in functions_result["functions"]:
            if func["name"].lower() == function_name.lower():
                target_function = func
                break
        
        if not target_function:
            return {"error": f"CRM function '{function_name}' not found"}
        
        if not target_function["interpretations"]:
            return {"error": f"No interpretations found for CRM function '{function_name}'"}
        
        auth_data = await fetch_bearer_token(user_id)
        if not auth_data:
            return {"error": "Authentication failed"}
        
        headers = {
            "Authorization": f"Bearer {auth_data['token']}",
            "Content-Type": "application/json",
            "accept": "*/*"
        }
        
        interpretation_id = target_function["interpretations"][0]["_id"]
        
        async with httpx.AsyncClient() as client:
            # Get detailed function info with situations
            response = await client.get(f"{BASE_URL}/crm-bot-function-detailed?interpretationId={interpretation_id}", headers=headers, timeout=10.0)
            response.raise_for_status()
            detailed_data = response.json()
            
            if detailed_data.get("status") != "SUCCESS":
                return {"error": "Failed to get detailed CRM function", "details": detailed_data}
            
            detailed_id = detailed_data["result"]["_id"]
            situations = detailed_data["result"].get("situation", [])
            
            # Find the situation to update
            target_situation = None
            for situation in situations:
                if situation["name"].lower() == old_situation.lower():
                    target_situation = situation
                    break
            
            if not target_situation:
                return {"error": f"Situation '{old_situation}' not found in function '{function_name}'"}
            
            situation_id = target_situation["id"]
            
            # Update the situation with correct payload
            update_payload = {
                "botFunctionDetailedId": detailed_id,
                "situationId": situation_id,
                "name": new_situation
            }
            
            # Use the correct URL for CRM functions
            response = await client.put(f"{BASE_URL}/crm-bot-function-detailed/situation", headers=headers, json=update_payload, timeout=10.0)
            response.raise_for_status()
            update_data = response.json()
            
            if update_data.get("status") != "SUCCESS":
                return {"error": "Failed to update CRM situation", "details": update_data}
            
            return {
                "success": True,
                "function_name": function_name,
                "old_situation": old_situation,
                "new_situation": new_situation
            }
            
    except Exception as e:
        logger.error(f"Error in update_situation_tool: {e}")
        return {"error": str(e)}