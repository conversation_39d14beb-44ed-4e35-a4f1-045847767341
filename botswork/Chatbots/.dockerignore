# Ignore Git-related files
.git
.gitignore

# Ignore build and distribution directories
build/
dist/
target/

# Ignore Python specific files and directories
*.pyc
__pycache__/
venv/
.pytest_cache/

# Ignore Node.js specific files and directories
node_modules/
npm-debug.log
yarn-error.log

# Ignore common development tools' configuration
.vscode/
.idea/
# .env

# Ignore log files
*.log

# Ignore OS-specific temporary files
.DS_Store
Thumbs.db

