import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from openai import AsyncOpenAI
from uuid import uuid4
import os
from dotenv import load_dotenv

# Import the new config loader
from receptionist_tools_config_loader import receptionist_tools_config

from models import ChatMessage, ChatResponse
from receptionist_tools import (
    list_receptionist_functions_tool,
    create_receptionist_function_tool,
    add_situation_tool,
    update_situation_tool,
    update_prompt_tool
)

# Load environment variables
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
X_API_KEY = os.getenv("X_API_KEY")
BASE_URL = os.getenv("BASE_URL")

# Initialize OpenAI client
client = AsyncOpenAI(api_key=OPENAI_API_KEY)

# In-memory stores
_conversation_history = {}  # {user_id: {session_id: [messages]}}
_token_cache = {}  # Cache for auth tokens

# Load tools configuration
def get_tools_config():
    """Get tools configuration from JSON file"""
    try:
        return receptionist_tools_config.get_openai_tools()
    except Exception as e:
        logging.error(f"Failed to load receptionist tools config: {e}")
        # Fallback to hardcoded tools if config fails
        return FALLBACK_TOOLS

def get_system_prompt():
    """Get system prompt from configuration"""
    try:
        return receptionist_tools_config.get_system_prompt()
    except Exception as e:
        logging.error(f"Failed to load system prompt: {e}")
        return FALLBACK_SYSTEM_PROMPT

# Fallback configurations (your original hardcoded ones)
FALLBACK_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "list_receptionist_functions",
            "description": "List existing receptionist functions and interpretations",
            "parameters": {
                "type": "object",
                "properties": {"user_id": {"type": "string"}},
                "required": ["user_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_receptionist_function",
            "description": "Create a new receptionist function with situation, prompt and action",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string"},
                    "function_name": {"type": "string"},
                    "situation_description": {"type": "string"}
                },
                "required": ["user_id", "function_name", "situation_description"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "update_situation",
            "description": "Update trigger situation for a receptionist function",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string"},
                    "function_name": {"type": "string"},
                    "old_situation": {"type": "string"},
                    "new_situation": {"type": "string"}
                },
                "required": ["user_id", "function_name", "old_situation", "new_situation"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "update_prompt",
            "description": "Update prompt/logic for a receptionist function",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string"},
                    "function_name": {"type": "string"},
                    "new_message": {"type": "string"},
                    "new_logic": {"type": "string"}
                },
                "required": ["user_id", "function_name", "new_message", "new_logic"]
            }
        }
    }
]

FALLBACK_SYSTEM_PROMPT = """You are an AI Receptionist Assistant designed to help users set up and manage receptionist bot functions intelligently.

Your capabilities include:
- Message Block Management (welcome message + behavior logic)
- Receptionist Function Management (create functions, add situations, edit prompt/logic)  
- Listing & Editing (show current functions, update situations/prompts)

Guidelines:
- Keep responses short and clear (1-2 sentences unless listing functions)
- The user_id is already provided in the system - never ask for it
- Use available tools directly based on user intent
- Always confirm results after tool use
- Focus on actionable information and clear next steps"""

async def handle_receptionist_chat(message: ChatMessage) -> ChatResponse:
    """Main handler for receptionist chat requests using GPT-4 with tools"""
    try:
        user_id = message.user_id
        user_message = message.message
        user_name = getattr(message, "user_name", "there")

        # Load configuration
        TOOLS = get_tools_config()
        SYSTEM_PROMPT = get_system_prompt()

        # Handle session management
        session_id = f"{user_id}:{uuid4()}" if user_message.lower() in ["reset", "start over", "new session"] else \
                     next((k for k in _conversation_history.get(user_id, {}).keys()), f"{user_id}:{uuid4()}")

        # Initialize conversation history
        if user_id not in _conversation_history:
            _conversation_history[user_id] = {}
        if session_id not in _conversation_history[user_id]:
            _conversation_history[user_id][session_id] = []
        
        # Clear history if user requests reset
        if user_message.lower() in ["reset", "start over", "new session"]:
            _conversation_history[user_id][session_id] = []
            welcome_msg = f"Hi {user_name}! I've reset our conversation. I'm your Receptionist Assistant, ready to help you manage receptionist functions, message blocks, and trigger situations. What would you like to work on today?"
            return ChatResponse(user_id=user_id, response=welcome_msg)

        # Build conversation context
        conversation_history = _conversation_history[user_id][session_id]
        messages = [{"role": "system", "content": SYSTEM_PROMPT}] + conversation_history + [{"role": "user", "content": user_message}]

        # Get environment configuration
        try:
            env_config = receptionist_tools_config.get_environment_config()
            model = env_config.get("api_model", "gpt-4o")
            max_tokens = env_config.get("max_tokens", 250)
            temperature = env_config.get("temperature", 0.7)
        except:
            model = "gpt-4o"
            max_tokens = 250
            temperature = 0.7

        # First GPT call to understand intent and use tools
        response = await client.chat.completions.create(
            model=model,
            messages=messages,
            tools=TOOLS,
            tool_choice="auto",
            temperature=temperature
        )

        assistant_message = response.choices[0].message
        
        # Handle tool calls
        if assistant_message.tool_calls:
            tool_results = []
            for tool_call in assistant_message.tool_calls:
                function_name = tool_call.function.name
                args = json.loads(tool_call.function.arguments)
                
                # Ensure user_id is correct
                args["user_id"] = user_id
                
                # Execute the appropriate tool
                if function_name == "list_receptionist_functions":
                    result = await list_receptionist_functions_tool(**args)
                elif function_name == "create_receptionist_function":
                    result = await create_receptionist_function_tool(**args)
                elif function_name == "add_situation":
                    result = await add_situation_tool(**args)
                elif function_name == "update_situation":
                    result = await update_situation_tool(**args)
                elif function_name == "update_prompt":
                    result = await update_prompt_tool(**args)
                else:
                    result = {"error": f"Unknown function: {function_name}"}
                
                tool_results.append({
                    "tool_call_id": tool_call.id,
                    "result": result
                })

            # Add assistant message with tool calls to history
            messages.append({
                "role": "assistant",
                "content": assistant_message.content or "",
                "tool_calls": [tc.model_dump() for tc in assistant_message.tool_calls]
            })

            # Add tool results to messages
            for tool_result in tool_results:
                messages.append({
                    "role": "tool",
                    "content": json.dumps(tool_result["result"]),
                    "tool_call_id": tool_result["tool_call_id"]
                })

            # Get final response from GPT
            final_response = await client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            bot_response = final_response.choices[0].message.content

        else:
            # Direct response without tool use
            bot_response = assistant_message.content

        # Update conversation history
        _conversation_history[user_id][session_id].extend([
            {"role": "user", "content": user_message},
            {"role": "assistant", "content": bot_response}
        ])

        # Limit history to last 20 messages
        _conversation_history[user_id][session_id] = _conversation_history[user_id][session_id][-20:]

        return ChatResponse(user_id=user_id, response=bot_response)

    except Exception as e:
        logging.error(f"Error in handle_receptionist_chat: {e}")
        error_response = "I apologize, but I encountered an issue processing your request. Please try again or contact support if the problem persists."
        return ChatResponse(user_id=message.user_id, response=error_response)

def clear_all_caches():
    """Clear all caches"""
    global _conversation_history, _token_cache
    _conversation_history.clear()
    _token_cache.clear()
    # Also reload tools configuration
    receptionist_tools_config.reload_config()