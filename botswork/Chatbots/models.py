from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Literal, Any

class ChatMessage(BaseModel):
    user_id: str
    message: str

class ChatResponse(BaseModel):
    user_id: str
    response: str


class ChatHistoryMessage(BaseModel):
    index: int                 # message index in the stored list
    role: str                  # "system" | "user" | "assistant"
    content: str
    state: Optional[str] = None
    extracted_data: Optional[Any] = None

class ChatHistoryResponse(BaseModel):
    user_id: str
    bot: Literal["receptionist", "phone"]
    total: int
    offset: int
    limit: int
    messages: List[ChatHistoryMessage]

class ConversationEntry(BaseModel):
    sender: str
    message: str

class CustomerProfile(BaseModel):

    id: str = Field(alias="_id")
    name: str
    email: List[Optional[str]]
    phone_number: List[Optional[str]] = Field(alias="phoneNumber")
    created_at: str = Field(alias="createdAt")
    updated_at: str = Field(alias="updatedAt")
    profile_summary: Optional[Dict[str, Any]] = None
    last_contacted: Optional[str] = None

class CRMFunction(BaseModel):
    """Model for CRM function data"""
    id: str = Field(alias="_id")
    name: str
    value: str
    interpretations: List[Dict[str, Any]] = []
    situations: List[str] = []

class Situation(BaseModel):
    """Model for situation data"""
    id: str
    name: str

class FunctionDetailed(BaseModel):
    """Model for detailed function data"""
    id: str = Field(alias="_id")
    user: str
    interpretation_id: str = Field(alias="interpretationId")
    situation: List[Situation] = []
    prompt: Dict[str, str] = {}
    created_at: str = Field(alias="createdAt")
    updated_at: str = Field(alias="updatedAt")

# Pydantic model for request body
class PostRequest(BaseModel):
    prompt: str
    user_id: str

# Pydantic model for response
class PostResponse(BaseModel):
    content: str
    hashtags: list[str]
    image_url: str