{"receptionist_tools": {"schema_version": "1.0.0", "environment": {"name": "prod", "api_model": "gpt-4o", "max_tokens": 250, "temperature": 0.7}, "tool_registry": {"function_management": {"id": "function_management", "title": "Receptionist Function Management Tools", "desc": "Tools for managing receptionist functions and their configurations", "tools": [{"id": "list_receptionist_functions", "function_name": "list_receptionist_functions", "title": "List Receptionist Functions", "description": "List existing receptionist functions and interpretations", "handler": "list_receptionist_functions_tool", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User ID"}}, "required": ["user_id"]}}, {"id": "create_receptionist_function", "function_name": "create_receptionist_function", "title": "Create Receptionist Function", "description": "Create a new receptionist function with situation, prompt and action", "handler": "create_receptionist_function_tool", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User ID"}, "function_name": {"type": "string", "description": "Name of the receptionist function"}, "situation_description": {"type": "string", "description": "When this function should trigger"}}, "required": ["user_id", "function_name", "situation_description"]}}]}, "situation_management": {"id": "situation_management", "title": "Situation Management Tools", "desc": "Tools for managing trigger situations within receptionist functions", "tools": [{"id": "add_situation", "function_name": "add_situation", "title": "Add Situation", "description": "Add a new trigger situation to an existing receptionist function", "handler": "add_situation_tool", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User ID"}, "function_name": {"type": "string", "description": "Name of the existing receptionist function"}, "situation_description": {"type": "string", "description": "New trigger situation to add"}}, "required": ["user_id", "function_name", "situation_description"]}}, {"id": "update_situation", "function_name": "update_situation", "title": "Update Situation", "description": "Update trigger situation for a receptionist function", "handler": "update_situation_tool", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User ID"}, "function_name": {"type": "string", "description": "Name of the receptionist function"}, "old_situation": {"type": "string", "description": "Current situation to update"}, "new_situation": {"type": "string", "description": "New situation description"}}, "required": ["user_id", "function_name", "old_situation", "new_situation"]}}]}, "prompt_management": {"id": "prompt_management", "title": "Prompt Management Tools", "desc": "Tools for managing message blocks and logic within receptionist functions", "tools": [{"id": "update_prompt", "function_name": "update_prompt", "title": "Update Prompt", "description": "Update prompt/logic for a receptionist function", "handler": "update_prompt_tool", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User ID"}, "function_name": {"type": "string", "description": "Name of the receptionist function"}, "new_message": {"type": "string", "description": "New message content"}, "new_logic": {"type": "string", "description": "New logic/behavior instructions"}}, "required": ["user_id", "function_name", "new_message", "new_logic"]}}]}}, "tool_mappings": {"list_receptionist_functions_tool": "receptionist_tools.list_receptionist_functions_tool", "create_receptionist_function_tool": "receptionist_tools.create_receptionist_function_tool", "add_situation_tool": "receptionist_tools.add_situation_tool", "update_situation_tool": "receptionist_tools.update_situation_tool", "update_prompt_tool": "receptionist_tools.update_prompt_tool"}, "system_prompt_config": {"base_prompt": "You are an AI Receptionist Assistant designed to help users set up and manage receptionist bot functions intelligently.", "capabilities": ["Message Block Management (welcome message + behavior logic)", "Receptionist Function Management (create functions, add situations, edit prompt/logic)", "Listing & Editing (show current functions, update situations/prompts)"], "guidelines": ["Keep responses short and clear (1-2 sentences unless listing functions)", "The user_id is already provided in the system - never ask for it", "Use available tools directly based on user intent", "Always confirm results after tool use", "Focus on actionable information and clear next steps"], "response_format": {"data_retrieval": "Present key information concisely with clear formatting", "actions": "Confirm what was done and suggest logical next steps", "errors": "Provide brief solution-oriented guidance without technical jargon"}}}}